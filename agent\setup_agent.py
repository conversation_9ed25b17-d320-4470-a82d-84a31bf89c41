#!/usr/bin/env python3
"""
Setup script for the Personal Assistant Agent.
"""
import os
import sys
import subprocess
import venv
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()


def create_virtual_environment():
    """Create a virtual environment for the agent."""
    console.print("[cyan]Creating virtual environment...[/cyan]")
    
    venv_path = Path("venv_agent")
    
    if venv_path.exists():
        console.print("[yellow]Virtual environment already exists[/yellow]")
        return True
    
    try:
        venv.create(venv_path, with_pip=True)
        console.print("[green]✓[/green] Virtual environment created")
        return True
    except Exception as e:
        console.print(f"[red]❌ Failed to create virtual environment: {e}[/red]")
        return False


def install_dependencies():
    """Install required dependencies."""
    console.print("[cyan]Installing dependencies...[/cyan]")
    
    # Determine the correct pip path
    if sys.platform == "win32":
        pip_path = Path("venv_agent") / "Scripts" / "pip.exe"
        python_path = Path("venv_agent") / "Scripts" / "python.exe"
    else:
        pip_path = Path("venv_agent") / "bin" / "pip"
        python_path = Path("venv_agent") / "bin" / "python"
    
    if not pip_path.exists():
        console.print(f"[red]❌ Pip not found at {pip_path}[/red]")
        return False
    
    try:
        # Upgrade pip first
        subprocess.run([
            str(python_path), "-m", "pip", "install", "--upgrade", "pip"
        ], check=True, capture_output=True)
        
        # Install requirements
        subprocess.run([
            str(pip_path), "install", "-r", "requirements_agent.txt"
        ], check=True, capture_output=True)
        
        console.print("[green]✓[/green] Dependencies installed")
        return True
        
    except subprocess.CalledProcessError as e:
        console.print(f"[red]❌ Failed to install dependencies: {e}[/red]")
        return False
    except Exception as e:
        console.print(f"[red]❌ Error installing dependencies: {e}[/red]")
        return False


def create_directories():
    """Create necessary directories."""
    console.print("[cyan]Creating directories...[/cyan]")
    
    directories = [
        "data",
        "data/conversations",
        "data/memory",
        "data/logs",
        "config/prompts"
    ]
    
    try:
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
        
        console.print("[green]✓[/green] Directories created")
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Failed to create directories: {e}[/red]")
        return False


def check_ollama():
    """Check if Ollama is available."""
    console.print("[cyan]Checking Ollama installation...[/cyan]")
    
    try:
        result = subprocess.run(
            ["ollama", "--version"], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        
        if result.returncode == 0:
            console.print("[green]✓[/green] Ollama is installed")
            return True
        else:
            console.print("[yellow]⚠[/yellow] Ollama not found or not working")
            return False
            
    except (subprocess.TimeoutExpired, FileNotFoundError):
        console.print("[yellow]⚠[/yellow] Ollama not found in PATH")
        return False
    except Exception as e:
        console.print(f"[yellow]⚠[/yellow] Error checking Ollama: {e}")
        return False


def check_rag_system():
    """Check if the RAG system is available."""
    console.print("[cyan]Checking RAG system...[/cyan]")
    
    rag_path = Path("../src/mcp/server.py")
    
    if rag_path.exists():
        console.print("[green]✓[/green] RAG system found")
        return True
    else:
        console.print("[yellow]⚠[/yellow] RAG system not found at expected location")
        console.print(f"    Expected: {rag_path.resolve()}")
        return False


def create_config_file():
    """Create a default configuration file."""
    console.print("[cyan]Creating configuration file...[/cyan]")
    
    config_content = """# Personal Assistant Agent Configuration
# Copy this to .env and modify as needed

# Agent Settings
AGENT_AGENT_NAME=Assistant
AGENT_AGENT_PERSONALITY=helpful, knowledgeable, and concise
AGENT_DEBUG_MODE=false

# Ollama Settings
AGENT_OLLAMA_BASE_URL=http://localhost:11434
AGENT_OLLAMA_MODEL=deepseek-r1
AGENT_OLLAMA_TEMPERATURE=0.7

# RAG System Settings
AGENT_RAG_SEARCH_THRESHOLD=0.3
AGENT_MAX_RAG_RESULTS=5
AGENT_RAG_SEARCH_TYPE=semantic

# Interface Settings
AGENT_WEB_PORT=8002
AGENT_API_PORT=8003

# Conversation Settings
AGENT_MAX_CONTEXT_LENGTH=4000
AGENT_CONVERSATION_MEMORY_LIMIT=50
AGENT_AUTO_SAVE_CONVERSATIONS=true

# Logging
AGENT_LOG_LEVEL=INFO
"""
    
    try:
        with open(".env.example", "w") as f:
            f.write(config_content)
        
        console.print("[green]✓[/green] Configuration example created (.env.example)")
        console.print("[yellow]💡[/yellow] Copy .env.example to .env and customize as needed")
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Failed to create config file: {e}[/red]")
        return False


def main():
    """Main setup function."""
    console.print(Panel.fit(
        "[bold blue]Personal Assistant Agent Setup[/bold blue]\n"
        "This will set up the agent environment and dependencies.",
        style="bold blue"
    ))
    
    # Change to the agent directory
    agent_dir = Path(__file__).parent
    os.chdir(agent_dir)
    
    steps = [
        ("Creating virtual environment", create_virtual_environment),
        ("Installing dependencies", install_dependencies),
        ("Creating directories", create_directories),
        ("Creating configuration", create_config_file),
        ("Checking Ollama", check_ollama),
        ("Checking RAG system", check_rag_system),
    ]
    
    success_count = 0
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        
        for description, func in steps:
            task = progress.add_task(description, total=None)
            
            if func():
                success_count += 1
                progress.update(task, description=f"✓ {description}")
            else:
                progress.update(task, description=f"❌ {description}")
            
            progress.remove_task(task)
    
    # Summary
    console.print(f"\n[bold]Setup Summary:[/bold]")
    console.print(f"✓ {success_count}/{len(steps)} steps completed successfully")
    
    if success_count == len(steps):
        console.print(Panel.fit(
            "[bold green]Setup completed successfully![/bold green]\n\n"
            "Next steps:\n"
            "1. Ensure your RAG system is running\n"
            "2. Start Ollama and pull the deepseek-r1 model:\n"
            "   ollama pull deepseek-r1\n"
            "3. Test the agent:\n"
            "   python agent_cli.py test\n"
            "4. Start chatting:\n"
            "   python agent_cli.py chat",
            style="bold green"
        ))
    else:
        console.print(Panel.fit(
            "[bold yellow]Setup completed with warnings[/bold yellow]\n\n"
            "Some components may not be available.\n"
            "Check the messages above and resolve any issues.",
            style="bold yellow"
        ))
    
    return success_count == len(steps)


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        console.print("\n[yellow]Setup interrupted by user[/yellow]")
        sys.exit(1)
    except Exception as e:
        console.print(f"\n[red]Setup failed with error: {e}[/red]")
        sys.exit(1)
