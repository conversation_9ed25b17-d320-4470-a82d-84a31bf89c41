@echo off
REM RAG System Startup Script for Windows
REM This script starts the RAG system services

echo.
echo ========================================
echo   RAG System with MCP Integration
echo ========================================
echo.

REM Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    echo ERROR: Virtual environment not found!
    echo Please run setup.py first to create the environment.
    pause
    exit /b 1
)

REM Activate virtual environment
echo [1/4] Activating virtual environment...
call venv\Scripts\activate.bat

REM Check if Ollama is running
echo [2/4] Checking Ollama service...
curl -s http://localhost:11434/api/version >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Ollama is not running on localhost:11434
    echo Please start Ollama manually if you need LLM integration
    echo.
)

REM Start the web interface
echo [3/4] Starting RAG Web Interface...
echo Web interface will be available at: http://localhost:8001
echo.
start "RAG Web Interface" cmd /k "venv\Scripts\python.exe -m uvicorn src.api.main:app --host localhost --port 8001 --reload"

REM Wait a moment for the web server to start
timeout /t 3 /nobreak >nul

REM Start the MCP server
echo [4/4] Starting MCP Server...
echo MCP server starting for AI agent integration...
echo.
start "RAG MCP Server" cmd /k "venv\Scripts\python.exe -m src.mcp.server"

echo.
echo ========================================
echo   RAG System Started Successfully!
echo ========================================
echo.
echo Services running:
echo   - Web Interface: http://localhost:8001
echo   - MCP Server: localhost (stdio)
echo.
echo Available commands:
echo   - CLI: python cli.py [command]
echo   - Stop: run stop_rag.bat
echo.
echo Press any key to open the web interface...
pause >nul

REM Open web interface in default browser
start http://localhost:8001

echo.
echo RAG System is now running!
echo Close this window or run stop_rag.bat to stop services.
echo.
