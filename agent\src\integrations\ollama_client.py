"""
Ollama client for LLM integration.
"""
import asyncio
import json
from typing import Dict, Any, List, Optional, AsyncGenerator
import httpx
from loguru import logger

from ...config.agent_settings import settings


class OllamaClient:
    """Client for communicating with Ollama LLM."""
    
    def __init__(self):
        """Initialize the Ollama client."""
        self.base_url = settings.ollama_base_url
        self.model = settings.ollama_model
        self.client: Optional[httpx.AsyncClient] = None
        self._connected = False
    
    async def initialize(self) -> bool:
        """Initialize the Ollama client."""
        try:
            # Create HTTP client
            self.client = httpx.AsyncClient(
                base_url=self.base_url,
                timeout=httpx.Timeout(settings.ollama_timeout)
            )
            
            # Test connection
            if await self.test_connection():
                self._connected = True
                logger.info(f"Ollama client connected to {self.base_url}")
                return True
            else:
                logger.error("Ollama connection test failed")
                await self.cleanup()
                return False
                
        except Exception as e:
            logger.error(f"Failed to initialize Ollama client: {e}")
            await self.cleanup()
            return False
    
    async def test_connection(self) -> bool:
        """Test the Ollama connection."""
        try:
            if not self.client:
                return False
            
            # Check if Ollama is running
            response = await self.client.get("/api/tags")
            if response.status_code != 200:
                logger.error(f"Ollama API returned status {response.status_code}")
                return False
            
            # Check if our model is available
            models_data = response.json()
            available_models = [model["name"] for model in models_data.get("models", [])]
            
            if self.model not in available_models:
                logger.warning(f"Model {self.model} not found. Available models: {available_models}")
                # Try to pull the model
                if not await self._pull_model():
                    return False
            
            logger.info(f"Ollama connection test passed. Model: {self.model}")
            return True
            
        except Exception as e:
            logger.error(f"Ollama connection test failed: {e}")
            return False
    
    async def _pull_model(self) -> bool:
        """Pull the model if it's not available."""
        try:
            logger.info(f"Pulling model {self.model}...")
            
            response = await self.client.post(
                "/api/pull",
                json={"name": self.model},
                timeout=300  # 5 minutes for model download
            )
            
            if response.status_code == 200:
                logger.info(f"Successfully pulled model {self.model}")
                return True
            else:
                logger.error(f"Failed to pull model {self.model}: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error pulling model {self.model}: {e}")
            return False
    
    async def generate_response(self, prompt: str, 
                              temperature: float = None,
                              max_tokens: int = None,
                              stream: bool = True) -> AsyncGenerator[str, None]:
        """Generate a response using the LLM."""
        if not self._connected or not self.client:
            yield "Error: Ollama client not connected"
            return
        
        try:
            # Prepare request parameters
            request_data = {
                "model": self.model,
                "prompt": prompt,
                "stream": stream,
                "options": {
                    "temperature": temperature or settings.ollama_temperature,
                    "num_predict": max_tokens or settings.ollama_max_tokens,
                }
            }
            
            if stream:
                # Streaming response
                async with self.client.stream(
                    "POST", 
                    "/api/generate",
                    json=request_data
                ) as response:
                    if response.status_code != 200:
                        yield f"Error: Ollama API returned status {response.status_code}"
                        return
                    
                    async for line in response.aiter_lines():
                        if line.strip():
                            try:
                                chunk_data = json.loads(line)
                                if "response" in chunk_data:
                                    yield chunk_data["response"]
                                
                                # Check if generation is done
                                if chunk_data.get("done", False):
                                    break
                                    
                            except json.JSONDecodeError:
                                logger.warning(f"Failed to parse JSON chunk: {line}")
                                continue
            else:
                # Non-streaming response
                response = await self.client.post("/api/generate", json=request_data)
                
                if response.status_code == 200:
                    result = response.json()
                    yield result.get("response", "")
                else:
                    yield f"Error: Ollama API returned status {response.status_code}"
                    
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            yield f"Error generating response: {str(e)}"
    
    async def generate_completion(self, prompt: str, 
                                temperature: float = None,
                                max_tokens: int = None) -> Optional[str]:
        """Generate a complete response (non-streaming)."""
        try:
            response_text = ""
            async for chunk in self.generate_response(
                prompt, temperature, max_tokens, stream=False
            ):
                response_text += chunk
            
            return response_text.strip() if response_text else None
            
        except Exception as e:
            logger.error(f"Error generating completion: {e}")
            return None
    
    async def chat_completion(self, messages: List[Dict[str, str]], 
                            temperature: float = None,
                            max_tokens: int = None,
                            stream: bool = True) -> AsyncGenerator[str, None]:
        """Generate a chat completion using the chat API."""
        if not self._connected or not self.client:
            yield "Error: Ollama client not connected"
            return
        
        try:
            # Prepare request parameters
            request_data = {
                "model": self.model,
                "messages": messages,
                "stream": stream,
                "options": {
                    "temperature": temperature or settings.ollama_temperature,
                    "num_predict": max_tokens or settings.ollama_max_tokens,
                }
            }
            
            if stream:
                # Streaming response
                async with self.client.stream(
                    "POST", 
                    "/api/chat",
                    json=request_data
                ) as response:
                    if response.status_code != 200:
                        yield f"Error: Ollama API returned status {response.status_code}"
                        return
                    
                    async for line in response.aiter_lines():
                        if line.strip():
                            try:
                                chunk_data = json.loads(line)
                                if "message" in chunk_data and "content" in chunk_data["message"]:
                                    yield chunk_data["message"]["content"]
                                
                                # Check if generation is done
                                if chunk_data.get("done", False):
                                    break
                                    
                            except json.JSONDecodeError:
                                logger.warning(f"Failed to parse JSON chunk: {line}")
                                continue
            else:
                # Non-streaming response
                response = await self.client.post("/api/chat", json=request_data)
                
                if response.status_code == 200:
                    result = response.json()
                    message = result.get("message", {})
                    yield message.get("content", "")
                else:
                    yield f"Error: Ollama API returned status {response.status_code}"
                    
        except Exception as e:
            logger.error(f"Error generating chat completion: {e}")
            yield f"Error generating chat completion: {str(e)}"
    
    async def get_model_info(self) -> Optional[Dict[str, Any]]:
        """Get information about the current model."""
        try:
            if not self.client:
                return None
            
            response = await self.client.post(
                "/api/show",
                json={"name": self.model}
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get model info: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting model info: {e}")
            return None
    
    async def list_models(self) -> List[str]:
        """List available models."""
        try:
            if not self.client:
                return []
            
            response = await self.client.get("/api/tags")
            
            if response.status_code == 200:
                models_data = response.json()
                return [model["name"] for model in models_data.get("models", [])]
            else:
                logger.error(f"Failed to list models: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"Error listing models: {e}")
            return []
    
    def is_connected(self) -> bool:
        """Check if the client is connected."""
        return self._connected and self.client is not None
    
    async def cleanup(self):
        """Clean up the Ollama client."""
        self._connected = False
        
        if self.client:
            await self.client.aclose()
            self.client = None
            logger.info("Ollama client cleaned up")
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()
