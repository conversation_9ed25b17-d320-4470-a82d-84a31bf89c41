"""
Memory management for the Personal Assistant Agent.
"""
import json
import sqlite3
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from loguru import logger

from ...config.agent_settings import settings


@dataclass
class MemoryItem:
    """Represents a memory item."""
    id: str
    content: str
    memory_type: str  # 'fact', 'preference', 'context', 'skill'
    importance: float  # 0.0 to 1.0
    created_at: datetime
    last_accessed: datetime
    access_count: int
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        data['last_accessed'] = self.last_accessed.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemoryItem':
        """Create from dictionary."""
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        data['last_accessed'] = datetime.fromisoformat(data['last_accessed'])
        return cls(**data)


class MemoryManager:
    """Manages agent memory and knowledge persistence."""
    
    def __init__(self):
        """Initialize the memory manager."""
        self.memory_dir = settings.memory_dir
        self.memory_dir.mkdir(exist_ok=True)
        
        self.db_path = self.memory_dir / "memory.db"
        self._init_database()
        
        # In-memory cache for frequently accessed items
        self._memory_cache: Dict[str, MemoryItem] = {}
        self._cache_size_limit = 100
    
    def _init_database(self):
        """Initialize the SQLite database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS memory_items (
                    id TEXT PRIMARY KEY,
                    content TEXT NOT NULL,
                    memory_type TEXT NOT NULL,
                    importance REAL NOT NULL,
                    created_at TEXT NOT NULL,
                    last_accessed TEXT NOT NULL,
                    access_count INTEGER NOT NULL,
                    metadata TEXT
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_memory_type 
                ON memory_items(memory_type)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_importance 
                ON memory_items(importance DESC)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_last_accessed 
                ON memory_items(last_accessed DESC)
            """)
            
            conn.commit()
    
    def store_memory(self, content: str, memory_type: str, importance: float = 0.5, 
                    metadata: Dict[str, Any] = None) -> str:
        """Store a new memory item."""
        import uuid
        
        memory_id = str(uuid.uuid4())
        now = datetime.now()
        
        memory_item = MemoryItem(
            id=memory_id,
            content=content,
            memory_type=memory_type,
            importance=max(0.0, min(1.0, importance)),  # Clamp to 0-1
            created_at=now,
            last_accessed=now,
            access_count=0,
            metadata=metadata or {}
        )
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO memory_items 
                    (id, content, memory_type, importance, created_at, last_accessed, access_count, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    memory_item.id,
                    memory_item.content,
                    memory_item.memory_type,
                    memory_item.importance,
                    memory_item.created_at.isoformat(),
                    memory_item.last_accessed.isoformat(),
                    memory_item.access_count,
                    json.dumps(memory_item.metadata)
                ))
                conn.commit()
            
            # Add to cache
            self._add_to_cache(memory_item)
            
            logger.debug(f"Stored memory: {memory_id} ({memory_type})")
            return memory_id
            
        except Exception as e:
            logger.error(f"Failed to store memory: {e}")
            raise
    
    def retrieve_memory(self, memory_id: str) -> Optional[MemoryItem]:
        """Retrieve a specific memory item."""
        # Check cache first
        if memory_id in self._memory_cache:
            memory_item = self._memory_cache[memory_id]
            self._update_access(memory_item)
            return memory_item
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT id, content, memory_type, importance, created_at, last_accessed, access_count, metadata
                    FROM memory_items WHERE id = ?
                """, (memory_id,))
                
                row = cursor.fetchone()
                if not row:
                    return None
                
                memory_item = MemoryItem(
                    id=row[0],
                    content=row[1],
                    memory_type=row[2],
                    importance=row[3],
                    created_at=datetime.fromisoformat(row[4]),
                    last_accessed=datetime.fromisoformat(row[5]),
                    access_count=row[6],
                    metadata=json.loads(row[7]) if row[7] else {}
                )
                
                self._update_access(memory_item)
                self._add_to_cache(memory_item)
                
                return memory_item
                
        except Exception as e:
            logger.error(f"Failed to retrieve memory {memory_id}: {e}")
            return None
    
    def search_memories(self, query: str, memory_type: str = None, 
                       limit: int = 10, min_importance: float = 0.0) -> List[MemoryItem]:
        """Search for relevant memories."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                sql = """
                    SELECT id, content, memory_type, importance, created_at, last_accessed, access_count, metadata
                    FROM memory_items 
                    WHERE content LIKE ? AND importance >= ?
                """
                params = [f"%{query}%", min_importance]
                
                if memory_type:
                    sql += " AND memory_type = ?"
                    params.append(memory_type)
                
                sql += " ORDER BY importance DESC, last_accessed DESC LIMIT ?"
                params.append(limit)
                
                cursor = conn.execute(sql, params)
                rows = cursor.fetchall()
                
                memories = []
                for row in rows:
                    memory_item = MemoryItem(
                        id=row[0],
                        content=row[1],
                        memory_type=row[2],
                        importance=row[3],
                        created_at=datetime.fromisoformat(row[4]),
                        last_accessed=datetime.fromisoformat(row[5]),
                        access_count=row[6],
                        metadata=json.loads(row[7]) if row[7] else {}
                    )
                    memories.append(memory_item)
                    self._add_to_cache(memory_item)
                
                return memories
                
        except Exception as e:
            logger.error(f"Failed to search memories: {e}")
            return []
    
    def get_memories_by_type(self, memory_type: str, limit: int = 50) -> List[MemoryItem]:
        """Get memories by type."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT id, content, memory_type, importance, created_at, last_accessed, access_count, metadata
                    FROM memory_items 
                    WHERE memory_type = ?
                    ORDER BY importance DESC, last_accessed DESC 
                    LIMIT ?
                """, (memory_type, limit))
                
                rows = cursor.fetchall()
                
                memories = []
                for row in rows:
                    memory_item = MemoryItem(
                        id=row[0],
                        content=row[1],
                        memory_type=row[2],
                        importance=row[3],
                        created_at=datetime.fromisoformat(row[4]),
                        last_accessed=datetime.fromisoformat(row[5]),
                        access_count=row[6],
                        metadata=json.loads(row[7]) if row[7] else {}
                    )
                    memories.append(memory_item)
                    self._add_to_cache(memory_item)
                
                return memories
                
        except Exception as e:
            logger.error(f"Failed to get memories by type {memory_type}: {e}")
            return []
    
    def update_memory(self, memory_id: str, content: str = None, 
                     importance: float = None, metadata: Dict[str, Any] = None) -> bool:
        """Update an existing memory item."""
        try:
            memory_item = self.retrieve_memory(memory_id)
            if not memory_item:
                return False
            
            # Update fields
            if content is not None:
                memory_item.content = content
            if importance is not None:
                memory_item.importance = max(0.0, min(1.0, importance))
            if metadata is not None:
                memory_item.metadata.update(metadata)
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE memory_items 
                    SET content = ?, importance = ?, metadata = ?, last_accessed = ?
                    WHERE id = ?
                """, (
                    memory_item.content,
                    memory_item.importance,
                    json.dumps(memory_item.metadata),
                    datetime.now().isoformat(),
                    memory_id
                ))
                conn.commit()
            
            # Update cache
            if memory_id in self._memory_cache:
                self._memory_cache[memory_id] = memory_item
            
            logger.debug(f"Updated memory: {memory_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update memory {memory_id}: {e}")
            return False
    
    def delete_memory(self, memory_id: str) -> bool:
        """Delete a memory item."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("DELETE FROM memory_items WHERE id = ?", (memory_id,))
                conn.commit()
                
                if cursor.rowcount > 0:
                    # Remove from cache
                    if memory_id in self._memory_cache:
                        del self._memory_cache[memory_id]
                    
                    logger.debug(f"Deleted memory: {memory_id}")
                    return True
                else:
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to delete memory {memory_id}: {e}")
            return False
    
    def _update_access(self, memory_item: MemoryItem):
        """Update access statistics for a memory item."""
        memory_item.last_accessed = datetime.now()
        memory_item.access_count += 1
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE memory_items 
                    SET last_accessed = ?, access_count = ?
                    WHERE id = ?
                """, (
                    memory_item.last_accessed.isoformat(),
                    memory_item.access_count,
                    memory_item.id
                ))
                conn.commit()
                
        except Exception as e:
            logger.warning(f"Failed to update access for memory {memory_item.id}: {e}")
    
    def _add_to_cache(self, memory_item: MemoryItem):
        """Add memory item to cache."""
        if len(self._memory_cache) >= self._cache_size_limit:
            # Remove least recently accessed item
            oldest_id = min(self._memory_cache.keys(), 
                           key=lambda k: self._memory_cache[k].last_accessed)
            del self._memory_cache[oldest_id]
        
        self._memory_cache[memory_item.id] = memory_item
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory statistics."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT COUNT(*) FROM memory_items")
                total_count = cursor.fetchone()[0]
                
                cursor = conn.execute("""
                    SELECT memory_type, COUNT(*) 
                    FROM memory_items 
                    GROUP BY memory_type
                """)
                type_counts = dict(cursor.fetchall())
                
                cursor = conn.execute("""
                    SELECT AVG(importance), MAX(importance), MIN(importance)
                    FROM memory_items
                """)
                importance_stats = cursor.fetchone()
                
                return {
                    'total_memories': total_count,
                    'by_type': type_counts,
                    'importance': {
                        'average': importance_stats[0] or 0.0,
                        'maximum': importance_stats[1] or 0.0,
                        'minimum': importance_stats[2] or 0.0
                    },
                    'cache_size': len(self._memory_cache)
                }
                
        except Exception as e:
            logger.error(f"Failed to get memory stats: {e}")
            return {}
    
    def cleanup_old_memories(self, days: int = 90, min_importance: float = 0.1) -> int:
        """Clean up old, low-importance memories."""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    DELETE FROM memory_items 
                    WHERE last_accessed < ? AND importance < ?
                """, (cutoff_date.isoformat(), min_importance))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                # Clear cache of deleted items
                self._memory_cache.clear()
                
                if deleted_count > 0:
                    logger.info(f"Cleaned up {deleted_count} old memories")
                
                return deleted_count
                
        except Exception as e:
            logger.error(f"Failed to cleanup old memories: {e}")
            return 0
