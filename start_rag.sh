#!/bin/bash
# RAG System Startup Script for Linux/macOS
# This script starts the RAG system services

echo ""
echo "========================================"
echo "   RAG System with MCP Integration"
echo "========================================"
echo ""

# Check if virtual environment exists
if [ ! -f "venv/bin/activate" ]; then
    echo "ERROR: Virtual environment not found!"
    echo "Please run setup.py first to create the environment."
    exit 1
fi

# Activate virtual environment
echo "[1/4] Activating virtual environment..."
source venv/bin/activate

# Check if Ollama is running
echo "[2/4] Checking Ollama service..."
if ! curl -s http://localhost:11434/api/version >/dev/null 2>&1; then
    echo "WARNING: Ollama is not running on localhost:11434"
    echo "Please start Ollama manually if you need LLM integration"
    echo ""
fi

# Create log directory
mkdir -p logs

# Start the web interface in background
echo "[3/4] Starting RAG Web Interface..."
echo "Web interface will be available at: http://localhost:8001"
echo ""
nohup python -m uvicorn src.api.main:app --host localhost --port 8001 --reload > logs/web_interface.log 2>&1 &
WEB_PID=$!
echo $WEB_PID > logs/web_interface.pid

# Wait a moment for the web server to start
sleep 3

# Start the MCP server in background
echo "[4/4] Starting MCP Server..."
echo "MCP server starting for AI agent integration..."
echo ""
nohup python -m src.mcp.server > logs/mcp_server.log 2>&1 &
MCP_PID=$!
echo $MCP_PID > logs/mcp_server.pid

echo ""
echo "========================================"
echo "   RAG System Started Successfully!"
echo "========================================"
echo ""
echo "Services running:"
echo "   - Web Interface: http://localhost:8001 (PID: $WEB_PID)"
echo "   - MCP Server: localhost (PID: $MCP_PID)"
echo ""
echo "Available commands:"
echo "   - CLI: python cli.py [command]"
echo "   - Stop: ./stop_rag.sh"
echo "   - Logs: tail -f logs/*.log"
echo ""
echo "Process IDs saved to logs/ directory"
echo "RAG System is now running in the background!"
echo ""

# Try to open web interface (works on macOS and some Linux distros)
if command -v open >/dev/null 2>&1; then
    open http://localhost:8001
elif command -v xdg-open >/dev/null 2>&1; then
    xdg-open http://localhost:8001
else
    echo "Open http://localhost:8001 in your browser to access the web interface"
fi
