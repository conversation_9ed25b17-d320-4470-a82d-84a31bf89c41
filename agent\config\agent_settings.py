"""
Configuration settings for the Personal Assistant Agent.
"""
from pathlib import Path
from typing import Optional, List
from pydantic import BaseSettings, Field


class AgentSettings(BaseSettings):
    """Configuration settings for the agent."""
    
    # Project paths
    agent_root: Path = Path(__file__).parent.parent
    data_dir: Path = agent_root / "data"
    conversations_dir: Path = data_dir / "conversations"
    memory_dir: Path = data_dir / "memory"
    logs_dir: Path = data_dir / "logs"
    
    # RAG System Integration (MCP)
    rag_mcp_command: str = "python -m src.mcp.server"
    rag_mcp_cwd: str = "../"  # Relative to agent directory
    rag_search_threshold: float = 0.3
    max_rag_results: int = 5
    rag_search_type: str = "semantic"  # semantic, hybrid, similarity
    
    # Ollama Integration
    ollama_base_url: str = "http://localhost:11434"
    ollama_model: str = "deepseek-r1"
    ollama_temperature: float = 0.7
    ollama_max_tokens: int = 2048
    ollama_timeout: int = 60
    
    # Conversation Management
    max_context_length: int = 4000
    conversation_memory_limit: int = 50
    conversation_history_days: int = 30
    auto_save_conversations: bool = True
    
    # Agent Behavior
    agent_name: str = "Assistant"
    agent_personality: str = "helpful, knowledgeable, and concise"
    default_response_style: str = "conversational"
    enable_rag_citations: bool = True
    
    # Interface Configuration
    cli_enabled: bool = True
    web_enabled: bool = True
    api_enabled: bool = True
    
    # Ports
    web_port: int = 8002
    api_port: int = 8003
    
    # Security
    cors_origins: List[str] = ["http://localhost:3000", "http://localhost:8002"]
    max_message_length: int = 10000
    rate_limit_requests: int = 100
    rate_limit_window: int = 60  # seconds
    
    # Performance
    response_timeout: int = 30
    max_concurrent_requests: int = 10
    enable_streaming: bool = True
    
    # Logging
    log_level: str = "INFO"
    log_file: Optional[str] = None
    log_rotation: str = "1 day"
    log_retention: str = "30 days"
    
    # Memory and Context
    enable_long_term_memory: bool = True
    memory_consolidation_interval: int = 3600  # seconds
    context_window_overlap: int = 200  # tokens
    
    # Development
    debug_mode: bool = False
    enable_profiling: bool = False
    mock_ollama: bool = False
    mock_rag: bool = False
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        env_prefix = "AGENT_"


# Global settings instance
settings = AgentSettings()

# Ensure directories exist
settings.data_dir.mkdir(exist_ok=True)
settings.conversations_dir.mkdir(exist_ok=True)
settings.memory_dir.mkdir(exist_ok=True)
settings.logs_dir.mkdir(exist_ok=True)
