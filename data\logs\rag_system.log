2025-07-08 16:58:19 | WARNING  | src.core.embeddings:_initialize_device:33 | CUDA requested but not available, falling back to CPU
2025-07-08 16:58:19 | INFO     | src.core.embeddings:_initialize_device:36 | Embedding device set to: cpu
2025-07-08 16:58:19 | INFO     | src.processing.pipeline:_initialize_extractors:42 | Initialized extractors for: ['.txt', '.md', '.rst', '.log', '.csv', '.json', '.xml', '.yaml', '.yml', '.pdf', '.docx', '.html', '.htm']
2025-07-08 16:58:19 | INFO     | src.core.embeddings:load_model:41 | Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-08 16:58:21 | INFO     | src.core.embeddings:load_model:43 | Embedding model loaded successfully
2025-07-08 16:58:21 | INFO     | src.core.vectorstore:_initialize_index:70 | Initialized FAISS index with dimension 384
2025-07-08 16:58:21 | INFO     | src.core.vectorstore:load:231 | Loaded vector store with 9 documents
2025-07-08 16:58:21 | INFO     | src.mcp.tools:_initialize_vector_store:46 | RAG tools initialized successfully
2025-07-08 16:59:03 | WARNING  | src.core.embeddings:_initialize_device:33 | CUDA requested but not available, falling back to CPU
2025-07-08 16:59:03 | INFO     | src.core.embeddings:_initialize_device:36 | Embedding device set to: cpu
2025-07-08 16:59:03 | INFO     | src.processing.pipeline:_initialize_extractors:42 | Initialized extractors for: ['.txt', '.md', '.rst', '.log', '.csv', '.json', '.xml', '.yaml', '.yml', '.pdf', '.docx', '.html', '.htm']
2025-07-08 16:59:03 | INFO     | src.core.embeddings:load_model:41 | Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-08 16:59:06 | INFO     | src.core.embeddings:load_model:43 | Embedding model loaded successfully
2025-07-08 16:59:06 | INFO     | src.core.vectorstore:_initialize_index:70 | Initialized FAISS index with dimension 384
2025-07-08 16:59:06 | INFO     | src.core.vectorstore:load:231 | Loaded vector store with 9 documents
2025-07-08 16:59:06 | INFO     | src.mcp.tools:_initialize_vector_store:46 | RAG tools initialized successfully
2025-07-08 16:59:17 | INFO     | src.api.main:search_documents:288 | API search request: RAG system
2025-07-08 16:59:17 | INFO     | src.mcp.tools:search_documents:68 | Searching documents: 'RAG system' (type=semantic, k=3)
2025-07-08 16:59:17 | INFO     | src.mcp.tools:search_documents:85 | Found 0 results for query
2025-07-08 17:03:38 | WARNING  | src.core.embeddings:_initialize_device:33 | CUDA requested but not available, falling back to CPU
2025-07-08 17:03:38 | INFO     | src.core.embeddings:_initialize_device:36 | Embedding device set to: cpu
2025-07-08 17:03:38 | INFO     | src.processing.pipeline:_initialize_extractors:42 | Initialized extractors for: ['.txt', '.md', '.rst', '.log', '.csv', '.json', '.xml', '.yaml', '.yml', '.pdf', '.docx', '.html', '.htm']
2025-07-08 17:03:38 | INFO     | src.core.embeddings:load_model:41 | Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-08 17:03:40 | INFO     | src.core.embeddings:load_model:43 | Embedding model loaded successfully
2025-07-08 17:03:40 | INFO     | src.core.vectorstore:_initialize_index:70 | Initialized FAISS index with dimension 384
2025-07-08 17:03:40 | INFO     | src.core.vectorstore:load:231 | Loaded vector store with 27 documents
2025-07-08 17:03:40 | INFO     | src.mcp.tools:_initialize_vector_store:46 | RAG tools initialized successfully
2025-07-08 17:47:32 | WARNING  | src.core.embeddings:_initialize_device:33 | CUDA requested but not available, falling back to CPU
2025-07-08 17:47:32 | INFO     | src.core.embeddings:_initialize_device:36 | Embedding device set to: cpu
2025-07-08 17:47:32 | INFO     | src.processing.pipeline:_initialize_extractors:42 | Initialized extractors for: ['.txt', '.md', '.rst', '.log', '.csv', '.json', '.xml', '.yaml', '.yml', '.pdf', '.docx', '.html', '.htm']
2025-07-08 17:47:32 | INFO     | src.core.embeddings:load_model:41 | Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-08 17:47:35 | INFO     | src.core.embeddings:load_model:43 | Embedding model loaded successfully
2025-07-08 17:47:35 | INFO     | src.core.vectorstore:_initialize_index:70 | Initialized FAISS index with dimension 384
2025-07-08 17:47:35 | INFO     | src.core.vectorstore:load:231 | Loaded vector store with 27 documents
2025-07-08 17:47:35 | INFO     | src.mcp.tools:_initialize_vector_store:46 | RAG tools initialized successfully
2025-07-08 17:49:01 | WARNING  | src.core.embeddings:_initialize_device:33 | CUDA requested but not available, falling back to CPU
2025-07-08 17:49:01 | INFO     | src.core.embeddings:_initialize_device:36 | Embedding device set to: cpu
2025-07-08 17:49:01 | INFO     | src.processing.pipeline:_initialize_extractors:42 | Initialized extractors for: ['.txt', '.md', '.rst', '.log', '.csv', '.json', '.xml', '.yaml', '.yml', '.pdf', '.docx', '.html', '.htm']
2025-07-08 17:49:01 | INFO     | src.core.embeddings:load_model:41 | Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-08 17:49:03 | INFO     | src.core.embeddings:load_model:43 | Embedding model loaded successfully
2025-07-08 17:49:03 | INFO     | src.core.vectorstore:_initialize_index:70 | Initialized FAISS index with dimension 384
2025-07-08 17:49:03 | INFO     | src.core.vectorstore:load:231 | Loaded vector store with 27 documents
2025-07-08 17:49:03 | INFO     | src.mcp.tools:_initialize_vector_store:46 | RAG tools initialized successfully
2025-07-08 17:52:55 | INFO     | src.api.main:upload_document:315 | API upload request: Amen's book in pdf.pdf
2025-07-08 17:52:55 | INFO     | src.mcp.tools:add_document:146 | Adding document: C:\Users\<USER>\AppData\Local\Temp\tmp472sa1f2.pdf
2025-07-08 17:52:55 | INFO     | src.processing.pipeline:process_file:124 | Processing file: C:\Users\<USER>\AppData\Local\Temp\tmp472sa1f2.pdf
2025-07-08 17:53:00 | INFO     | src.core.vectorstore:add_documents:101 | Added 137 documents to vector store
2025-07-08 17:53:00 | INFO     | src.processing.pipeline:process_file:168 | Successfully processed C:\Users\<USER>\AppData\Local\Temp\tmp472sa1f2.pdf: 137 chunks
2025-07-08 17:53:00 | INFO     | src.core.vectorstore:save:196 | Saved vector store to C:\Users\<USER>\dev\RAG\data\vectorstore\rag_documents
2025-07-08 17:53:00 | INFO     | src.mcp.tools:add_document:168 | Document added and vector store saved
2025-07-08 17:53:28 | INFO     | src.api.main:search_documents:288 | API search request: How should I deal with burnout?
2025-07-08 17:53:28 | INFO     | src.mcp.tools:search_documents:68 | Searching documents: 'How should I deal with burnout?' (type=semantic, k=5)
2025-07-08 17:53:28 | INFO     | src.mcp.tools:search_documents:85 | Found 0 results for query
2025-07-08 17:53:34 | INFO     | src.api.main:search_documents:288 | API search request: How should I deal with burnout?
2025-07-08 17:53:34 | INFO     | src.mcp.tools:search_documents:68 | Searching documents: 'How should I deal with burnout?' (type=hybrid, k=5)
2025-07-08 17:53:34 | INFO     | src.mcp.tools:search_documents:85 | Found 5 results for query
