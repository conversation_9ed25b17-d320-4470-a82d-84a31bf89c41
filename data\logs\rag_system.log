2025-07-08 16:58:19 | WARNING  | src.core.embeddings:_initialize_device:33 | CUDA requested but not available, falling back to CPU
2025-07-08 16:58:19 | INFO     | src.core.embeddings:_initialize_device:36 | Embedding device set to: cpu
2025-07-08 16:58:19 | INFO     | src.processing.pipeline:_initialize_extractors:42 | Initialized extractors for: ['.txt', '.md', '.rst', '.log', '.csv', '.json', '.xml', '.yaml', '.yml', '.pdf', '.docx', '.html', '.htm']
2025-07-08 16:58:19 | INFO     | src.core.embeddings:load_model:41 | Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-08 16:58:21 | INFO     | src.core.embeddings:load_model:43 | Embedding model loaded successfully
2025-07-08 16:58:21 | INFO     | src.core.vectorstore:_initialize_index:70 | Initialized FAISS index with dimension 384
2025-07-08 16:58:21 | INFO     | src.core.vectorstore:load:231 | Loaded vector store with 9 documents
2025-07-08 16:58:21 | INFO     | src.mcp.tools:_initialize_vector_store:46 | RAG tools initialized successfully
2025-07-08 16:59:03 | WARNING  | src.core.embeddings:_initialize_device:33 | CUDA requested but not available, falling back to CPU
2025-07-08 16:59:03 | INFO     | src.core.embeddings:_initialize_device:36 | Embedding device set to: cpu
2025-07-08 16:59:03 | INFO     | src.processing.pipeline:_initialize_extractors:42 | Initialized extractors for: ['.txt', '.md', '.rst', '.log', '.csv', '.json', '.xml', '.yaml', '.yml', '.pdf', '.docx', '.html', '.htm']
2025-07-08 16:59:03 | INFO     | src.core.embeddings:load_model:41 | Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-08 16:59:06 | INFO     | src.core.embeddings:load_model:43 | Embedding model loaded successfully
2025-07-08 16:59:06 | INFO     | src.core.vectorstore:_initialize_index:70 | Initialized FAISS index with dimension 384
2025-07-08 16:59:06 | INFO     | src.core.vectorstore:load:231 | Loaded vector store with 9 documents
2025-07-08 16:59:06 | INFO     | src.mcp.tools:_initialize_vector_store:46 | RAG tools initialized successfully
2025-07-08 16:59:17 | INFO     | src.api.main:search_documents:288 | API search request: RAG system
2025-07-08 16:59:17 | INFO     | src.mcp.tools:search_documents:68 | Searching documents: 'RAG system' (type=semantic, k=3)
2025-07-08 16:59:17 | INFO     | src.mcp.tools:search_documents:85 | Found 0 results for query
2025-07-08 17:03:38 | WARNING  | src.core.embeddings:_initialize_device:33 | CUDA requested but not available, falling back to CPU
2025-07-08 17:03:38 | INFO     | src.core.embeddings:_initialize_device:36 | Embedding device set to: cpu
2025-07-08 17:03:38 | INFO     | src.processing.pipeline:_initialize_extractors:42 | Initialized extractors for: ['.txt', '.md', '.rst', '.log', '.csv', '.json', '.xml', '.yaml', '.yml', '.pdf', '.docx', '.html', '.htm']
2025-07-08 17:03:38 | INFO     | src.core.embeddings:load_model:41 | Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-08 17:03:40 | INFO     | src.core.embeddings:load_model:43 | Embedding model loaded successfully
2025-07-08 17:03:40 | INFO     | src.core.vectorstore:_initialize_index:70 | Initialized FAISS index with dimension 384
2025-07-08 17:03:40 | INFO     | src.core.vectorstore:load:231 | Loaded vector store with 27 documents
2025-07-08 17:03:40 | INFO     | src.mcp.tools:_initialize_vector_store:46 | RAG tools initialized successfully
2025-07-08 17:47:32 | WARNING  | src.core.embeddings:_initialize_device:33 | CUDA requested but not available, falling back to CPU
2025-07-08 17:47:32 | INFO     | src.core.embeddings:_initialize_device:36 | Embedding device set to: cpu
2025-07-08 17:47:32 | INFO     | src.processing.pipeline:_initialize_extractors:42 | Initialized extractors for: ['.txt', '.md', '.rst', '.log', '.csv', '.json', '.xml', '.yaml', '.yml', '.pdf', '.docx', '.html', '.htm']
2025-07-08 17:47:32 | INFO     | src.core.embeddings:load_model:41 | Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-08 17:47:35 | INFO     | src.core.embeddings:load_model:43 | Embedding model loaded successfully
2025-07-08 17:47:35 | INFO     | src.core.vectorstore:_initialize_index:70 | Initialized FAISS index with dimension 384
2025-07-08 17:47:35 | INFO     | src.core.vectorstore:load:231 | Loaded vector store with 27 documents
2025-07-08 17:47:35 | INFO     | src.mcp.tools:_initialize_vector_store:46 | RAG tools initialized successfully
2025-07-08 17:49:01 | WARNING  | src.core.embeddings:_initialize_device:33 | CUDA requested but not available, falling back to CPU
2025-07-08 17:49:01 | INFO     | src.core.embeddings:_initialize_device:36 | Embedding device set to: cpu
2025-07-08 17:49:01 | INFO     | src.processing.pipeline:_initialize_extractors:42 | Initialized extractors for: ['.txt', '.md', '.rst', '.log', '.csv', '.json', '.xml', '.yaml', '.yml', '.pdf', '.docx', '.html', '.htm']
2025-07-08 17:49:01 | INFO     | src.core.embeddings:load_model:41 | Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-08 17:49:03 | INFO     | src.core.embeddings:load_model:43 | Embedding model loaded successfully
2025-07-08 17:49:03 | INFO     | src.core.vectorstore:_initialize_index:70 | Initialized FAISS index with dimension 384
2025-07-08 17:49:03 | INFO     | src.core.vectorstore:load:231 | Loaded vector store with 27 documents
2025-07-08 17:49:03 | INFO     | src.mcp.tools:_initialize_vector_store:46 | RAG tools initialized successfully
