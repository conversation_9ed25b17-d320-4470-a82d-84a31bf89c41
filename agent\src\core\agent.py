"""
Core agent implementation for the Personal Assistant Agent.
"""
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional, AsyncGenerator
from loguru import logger

from .conversation import ConversationManager, Message
from .memory import MemoryManager
from ..integrations.mcp_client import MC<PERSON>lient
from ..integrations.ollama_client import OllamaClient
from ..utils.prompts import PromptManager
from ...config.agent_settings import settings


class PersonalAssistantAgent:
    """Main agent class that orchestrates all components."""
    
    def __init__(self):
        """Initialize the agent."""
        self.conversation_manager = ConversationManager()
        self.memory_manager = MemoryManager()
        self.mcp_client = MCPClient()
        self.ollama_client = OllamaClient()
        self.prompt_manager = PromptManager()
        
        self._initialized = False
        self._running = False
    
    async def initialize(self) -> bool:
        """Initialize all agent components."""
        if self._initialized:
            return True
        
        logger.info("Initializing Personal Assistant Agent...")
        
        try:
            # Initialize MCP client (RAG system connection)
            logger.info("Connecting to RAG system via MCP...")
            if not await self.mcp_client.initialize():
                logger.error("Failed to initialize MCP client")
                return False
            
            # Initialize Ollama client
            logger.info("Connecting to Ollama...")
            if not await self.ollama_client.initialize():
                logger.error("Failed to initialize Ollama client")
                return False
            
            # Test connections
            logger.info("Testing system connections...")
            if not await self._test_connections():
                logger.error("Connection tests failed")
                return False
            
            self._initialized = True
            logger.info("Agent initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize agent: {e}")
            return False
    
    async def _test_connections(self) -> bool:
        """Test all system connections."""
        try:
            # Test RAG system
            rag_test = await self.mcp_client.list_collections()
            if not rag_test:
                logger.error("RAG system connection test failed")
                return False
            
            # Test Ollama
            ollama_test = await self.ollama_client.test_connection()
            if not ollama_test:
                logger.error("Ollama connection test failed")
                return False
            
            logger.info("All connection tests passed")
            return True
            
        except Exception as e:
            logger.error(f"Connection test error: {e}")
            return False
    
    async def start(self) -> bool:
        """Start the agent."""
        if not self._initialized:
            if not await self.initialize():
                return False
        
        self._running = True
        logger.info("Personal Assistant Agent started")
        return True
    
    async def stop(self):
        """Stop the agent."""
        self._running = False
        
        # Cleanup connections
        await self.mcp_client.cleanup()
        await self.ollama_client.cleanup()
        
        logger.info("Personal Assistant Agent stopped")
    
    async def process_message(self, user_input: str, 
                            conversation_id: str = None) -> AsyncGenerator[str, None]:
        """Process a user message and generate response."""
        if not self._running:
            yield "Error: Agent is not running"
            return
        
        try:
            # Load or create conversation
            if conversation_id:
                conversation = self.conversation_manager.load_conversation(conversation_id)
                if not conversation:
                    logger.warning(f"Conversation {conversation_id} not found, creating new one")
                    conversation = self.conversation_manager.create_conversation()
            else:
                conversation = self.conversation_manager.get_current_conversation()
                if not conversation:
                    conversation = self.conversation_manager.create_conversation()
            
            # Add user message
            user_message = self.conversation_manager.add_message("user", user_input)
            logger.info(f"Processing user message: {user_input[:100]}...")
            
            # Analyze query and retrieve relevant information
            rag_context = await self._retrieve_rag_context(user_input)
            memory_context = await self._retrieve_memory_context(user_input)
            
            # Generate response
            response_text = ""
            async for response_chunk in self._generate_response(
                user_input, rag_context, memory_context
            ):
                response_text += response_chunk
                yield response_chunk
            
            # Add assistant response to conversation
            self.conversation_manager.add_message("assistant", response_text)
            
            # Store important information in memory
            await self._update_memory(user_input, response_text, rag_context)
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            yield f"I apologize, but I encountered an error: {str(e)}"
    
    async def _retrieve_rag_context(self, query: str) -> List[Dict[str, Any]]:
        """Retrieve relevant context from RAG system."""
        try:
            # Search for relevant documents
            search_results = await self.mcp_client.search_documents(
                query=query,
                k=settings.max_rag_results,
                score_threshold=settings.rag_search_threshold,
                search_type=settings.rag_search_type
            )
            
            if search_results and search_results.get('results'):
                logger.debug(f"Retrieved {len(search_results['results'])} RAG results")
                return search_results['results']
            else:
                logger.debug("No relevant RAG results found")
                return []
                
        except Exception as e:
            logger.error(f"Error retrieving RAG context: {e}")
            return []
    
    async def _retrieve_memory_context(self, query: str) -> List[Dict[str, Any]]:
        """Retrieve relevant context from agent memory."""
        try:
            # Search for relevant memories
            memories = self.memory_manager.search_memories(
                query=query,
                limit=5,
                min_importance=0.3
            )
            
            if memories:
                logger.debug(f"Retrieved {len(memories)} memory items")
                return [memory.to_dict() for memory in memories]
            else:
                logger.debug("No relevant memories found")
                return []
                
        except Exception as e:
            logger.error(f"Error retrieving memory context: {e}")
            return []
    
    async def _generate_response(self, user_input: str, rag_context: List[Dict[str, Any]], 
                               memory_context: List[Dict[str, Any]]) -> AsyncGenerator[str, None]:
        """Generate response using LLM with context."""
        try:
            # Get conversation context
            conversation_context = self.conversation_manager.get_conversation_context()
            
            # Build prompt
            prompt = self.prompt_manager.build_response_prompt(
                user_input=user_input,
                conversation_context=conversation_context,
                rag_context=rag_context,
                memory_context=memory_context
            )
            
            # Generate response with streaming
            async for chunk in self.ollama_client.generate_response(prompt):
                yield chunk
                
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            yield f"I apologize, but I encountered an error generating a response: {str(e)}"
    
    async def _update_memory(self, user_input: str, response: str, 
                           rag_context: List[Dict[str, Any]]):
        """Update agent memory with important information."""
        try:
            # Extract and store important facts or preferences
            # This is a simplified implementation - could be enhanced with NLP
            
            # Store user preferences if detected
            if any(word in user_input.lower() for word in ['prefer', 'like', 'dislike', 'want']):
                self.memory_manager.store_memory(
                    content=f"User preference: {user_input}",
                    memory_type="preference",
                    importance=0.7,
                    metadata={"timestamp": datetime.now().isoformat()}
                )
            
            # Store factual information from RAG context
            if rag_context:
                for context_item in rag_context[:2]:  # Store top 2 most relevant
                    self.memory_manager.store_memory(
                        content=f"Relevant information: {context_item.get('content', '')[:500]}",
                        memory_type="context",
                        importance=0.5,
                        metadata={
                            "source": context_item.get('doc_id', ''),
                            "score": context_item.get('score', 0.0),
                            "timestamp": datetime.now().isoformat()
                        }
                    )
            
        except Exception as e:
            logger.warning(f"Error updating memory: {e}")
    
    def get_conversation_list(self) -> List[Dict[str, Any]]:
        """Get list of conversations."""
        return self.conversation_manager.list_conversations()
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory statistics."""
        return self.memory_manager.get_memory_stats()
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status."""
        return {
            'initialized': self._initialized,
            'running': self._running,
            'mcp_connected': self.mcp_client.is_connected() if hasattr(self.mcp_client, 'is_connected') else False,
            'ollama_connected': self.ollama_client.is_connected() if hasattr(self.ollama_client, 'is_connected') else False,
            'conversations': len(self.get_conversation_list()),
            'memory_stats': self.get_memory_stats()
        }
