#!/usr/bin/env python3
"""
Cross-platform RAG System Manager
Provides easy start/stop functionality for the RAG system services.
"""
import os
import sys
import time
import signal
import subprocess
import platform
from pathlib import Path
import psutil
import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich import print as rprint

app = typer.Typer(help="RAG System Manager - Start, stop, and manage RAG services")
console = Console()


class RAGManager:
    """Manages RAG system services across platforms."""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.logs_dir = self.base_dir / "logs"
        self.logs_dir.mkdir(exist_ok=True)
        self.pid_files = {
            "web": self.logs_dir / "web_interface.pid",
            "mcp": self.logs_dir / "mcp_server.pid"
        }
        self.is_windows = platform.system() == "Windows"
    
    def get_venv_python(self):
        """Get the path to the virtual environment Python executable."""
        if self.is_windows:
            return self.base_dir / "venv" / "Scripts" / "python.exe"
        else:
            return self.base_dir / "venv" / "bin" / "python"
    
    def check_venv(self):
        """Check if virtual environment exists."""
        python_path = self.get_venv_python()
        if not python_path.exists():
            console.print("[red]❌ Virtual environment not found![/red]")
            console.print("Please run [cyan]python setup.py[/cyan] first to create the environment.")
            return False
        return True
    
    def check_ollama(self):
        """Check if Ollama is running."""
        try:
            import requests
            response = requests.get("http://localhost:11434/api/version", timeout=2)
            return response.status_code == 200
        except:
            return False
    
    def save_pid(self, service, pid):
        """Save process ID to file."""
        with open(self.pid_files[service], 'w') as f:
            f.write(str(pid))
    
    def load_pid(self, service):
        """Load process ID from file."""
        try:
            with open(self.pid_files[service], 'r') as f:
                return int(f.read().strip())
        except:
            return None
    
    def is_process_running(self, pid):
        """Check if a process is running."""
        try:
            return psutil.pid_exists(pid)
        except:
            return False
    
    def kill_process(self, pid, force=False):
        """Kill a process by PID."""
        try:
            process = psutil.Process(pid)
            if force:
                process.kill()
            else:
                process.terminate()
            return True
        except:
            return False
    
    def find_processes_by_port(self, port):
        """Find processes using a specific port."""
        pids = []
        try:
            for conn in psutil.net_connections():
                if conn.laddr.port == port:
                    pids.append(conn.pid)
        except:
            pass
        return pids
    
    def find_processes_by_cmdline(self, pattern):
        """Find processes by command line pattern."""
        pids = []
        try:
            for proc in psutil.process_iter(['pid', 'cmdline']):
                cmdline = ' '.join(proc.info['cmdline'] or [])
                if pattern in cmdline:
                    pids.append(proc.info['pid'])
        except:
            pass
        return pids
    
    def start_service(self, service_name, command, log_file):
        """Start a service and return its PID."""
        python_path = self.get_venv_python()
        
        # Prepare command
        if isinstance(command, str):
            cmd = [str(python_path)] + command.split()
        else:
            cmd = [str(python_path)] + command
        
        # Start process
        with open(log_file, 'w') as f:
            if self.is_windows:
                process = subprocess.Popen(
                    cmd, 
                    stdout=f, 
                    stderr=subprocess.STDOUT,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
                )
            else:
                process = subprocess.Popen(
                    cmd,
                    stdout=f,
                    stderr=subprocess.STDOUT,
                    preexec_fn=os.setsid
                )
        
        return process.pid
    
    def get_service_status(self):
        """Get status of all services."""
        status = {}
        
        for service in ["web", "mcp"]:
            pid = self.load_pid(service)
            if pid and self.is_process_running(pid):
                status[service] = {"running": True, "pid": pid}
            else:
                status[service] = {"running": False, "pid": None}
        
        return status
    
    def start_all(self):
        """Start all RAG services."""
        console.print(Panel.fit("🚀 Starting RAG System", style="bold blue"))
        
        # Check prerequisites
        if not self.check_venv():
            return False
        
        console.print("[green]✓[/green] Virtual environment found")
        
        # Check Ollama
        if self.check_ollama():
            console.print("[green]✓[/green] Ollama is running")
        else:
            console.print("[yellow]⚠[/yellow] Ollama not detected (optional for basic functionality)")
        
        # Start web interface
        console.print("\n[cyan]Starting Web Interface...[/cyan]")
        try:
            web_pid = self.start_service(
                "web",
                ["-m", "uvicorn", "src.api.main:app", "--host", "localhost", "--port", "8001", "--reload"],
                self.logs_dir / "web_interface.log"
            )
            self.save_pid("web", web_pid)
            console.print(f"[green]✓[/green] Web Interface started (PID: {web_pid})")
            time.sleep(2)  # Wait for startup
        except Exception as e:
            console.print(f"[red]❌ Failed to start Web Interface: {e}[/red]")
            return False
        
        # Start MCP server
        console.print("[cyan]Starting MCP Server...[/cyan]")
        try:
            mcp_pid = self.start_service(
                "mcp",
                ["-m", "src.mcp.server"],
                self.logs_dir / "mcp_server.log"
            )
            self.save_pid("mcp", mcp_pid)
            console.print(f"[green]✓[/green] MCP Server started (PID: {mcp_pid})")
        except Exception as e:
            console.print(f"[red]❌ Failed to start MCP Server: {e}[/red]")
            return False
        
        # Success message
        console.print(Panel.fit(
            "[green]RAG System Started Successfully![/green]\n\n"
            "🌐 Web Interface: http://localhost:8001\n"
            "🔧 MCP Server: Running\n"
            "📝 CLI: python cli.py [command]\n"
            "🛑 Stop: python rag_manager.py stop",
            style="bold green"
        ))
        
        # Try to open browser
        try:
            import webbrowser
            webbrowser.open("http://localhost:8001")
        except:
            pass
        
        return True
    
    def stop_all(self):
        """Stop all RAG services."""
        console.print(Panel.fit("🛑 Stopping RAG System", style="bold red"))
        
        stopped_services = []
        
        # Stop services by PID
        for service in ["web", "mcp"]:
            pid = self.load_pid(service)
            if pid and self.is_process_running(pid):
                console.print(f"[yellow]Stopping {service.upper()} service (PID: {pid})...[/yellow]")
                if self.kill_process(pid):
                    stopped_services.append(service)
                    console.print(f"[green]✓[/green] {service.upper()} service stopped")
                else:
                    console.print(f"[red]❌ Failed to stop {service.upper()} service[/red]")
                
                # Remove PID file
                try:
                    self.pid_files[service].unlink()
                except:
                    pass
        
        # Cleanup remaining processes
        console.print("[cyan]Cleaning up remaining processes...[/cyan]")
        
        # Kill processes on port 8001
        port_pids = self.find_processes_by_port(8001)
        for pid in port_pids:
            self.kill_process(pid, force=True)
        
        # Kill processes by command line pattern
        patterns = ["src.api.main", "src.mcp.server", "uvicorn.*src.api.main"]
        for pattern in patterns:
            pids = self.find_processes_by_cmdline(pattern)
            for pid in pids:
                self.kill_process(pid, force=True)
        
        console.print(Panel.fit(
            "[green]RAG System Stopped Successfully![/green]\n\n"
            "All services have been terminated.\n"
            "Restart with: python rag_manager.py start",
            style="bold green"
        ))
        
        return True


@app.command()
def start():
    """Start the RAG system services."""
    manager = RAGManager()
    manager.start_all()


@app.command()
def stop():
    """Stop the RAG system services."""
    manager = RAGManager()
    manager.stop_all()


@app.command()
def status():
    """Show status of RAG system services."""
    manager = RAGManager()
    service_status = manager.get_service_status()
    
    table = Table(title="RAG System Status")
    table.add_column("Service", style="cyan")
    table.add_column("Status", style="bold")
    table.add_column("PID", style="yellow")
    table.add_column("URL/Info", style="blue")
    
    for service, info in service_status.items():
        if info["running"]:
            status_text = "[green]Running[/green]"
            pid_text = str(info["pid"])
        else:
            status_text = "[red]Stopped[/red]"
            pid_text = "-"
        
        if service == "web":
            url = "http://localhost:8001" if info["running"] else "-"
        else:
            url = "MCP Server" if info["running"] else "-"
        
        table.add_row(service.upper(), status_text, pid_text, url)
    
    console.print(table)
    
    # Check Ollama
    manager = RAGManager()
    if manager.check_ollama():
        console.print("\n[green]✓[/green] Ollama is running on localhost:11434")
    else:
        console.print("\n[yellow]⚠[/yellow] Ollama not detected on localhost:11434")


@app.command()
def restart():
    """Restart the RAG system services."""
    manager = RAGManager()
    console.print("[yellow]Restarting RAG System...[/yellow]")
    manager.stop_all()
    time.sleep(2)
    manager.start_all()


@app.command()
def logs(service: str = typer.Argument("all", help="Service to show logs for (web, mcp, or all)")):
    """Show logs for RAG services."""
    manager = RAGManager()
    
    if service == "all":
        services = ["web", "mcp"]
    elif service in ["web", "mcp"]:
        services = [service]
    else:
        console.print(f"[red]Unknown service: {service}[/red]")
        console.print("Available services: web, mcp, all")
        return
    
    for svc in services:
        log_file = manager.logs_dir / f"{svc}_{'interface' if svc == 'web' else 'server'}.log"
        if log_file.exists():
            console.print(Panel.fit(f"📝 {svc.upper()} Service Logs", style="bold blue"))
            with open(log_file, 'r') as f:
                content = f.read()
                if content:
                    console.print(content[-2000:])  # Show last 2000 characters
                else:
                    console.print("[yellow]No logs available[/yellow]")
        else:
            console.print(f"[yellow]No log file found for {svc} service[/yellow]")


if __name__ == "__main__":
    app()
