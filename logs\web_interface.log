INFO:     Will watch for changes in these directories: ['C:\\Users\\<USER>\\dev\\RAG']
INFO:     Uvicorn running on http://localhost:8001 (Press CTRL+C to quit)
INFO:     Started reloader process [26780] using WatchFiles
[32m2025-07-08 17:49:01[0m | [33m[1mWARNING [0m | [36msrc.core.embeddings[0m:[36m_initialize_device[0m:[36m33[0m | [33m[1mCUDA requested but not available, falling back to CPU[0m
[32m2025-07-08 17:49:01[0m | [1mINFO    [0m | [36msrc.core.embeddings[0m:[36m_initialize_device[0m:[36m36[0m | [1mEmbedding device set to: cpu[0m
[32m2025-07-08 17:49:01[0m | [1mINFO    [0m | [36msrc.processing.pipeline[0m:[36m_initialize_extractors[0m:[36m42[0m | [1mInitialized extractors for: ['.txt', '.md', '.rst', '.log', '.csv', '.json', '.xml', '.yaml', '.yml', '.pdf', '.docx', '.html', '.htm'][0m
[32m2025-07-08 17:49:01[0m | [1mINFO    [0m | [36msrc.core.embeddings[0m:[36mload_model[0m:[36m41[0m | [1mLoading embedding model: sentence-transformers/all-MiniLM-L6-v2[0m
[32m2025-07-08 17:49:03[0m | [1mINFO    [0m | [36msrc.core.embeddings[0m:[36mload_model[0m:[36m43[0m | [1mEmbedding model loaded successfully[0m
[32m2025-07-08 17:49:03[0m | [1mINFO    [0m | [36msrc.core.vectorstore[0m:[36m_initialize_index[0m:[36m70[0m | [1mInitialized FAISS index with dimension 384[0m
[32m2025-07-08 17:49:03[0m | [1mINFO    [0m | [36msrc.core.vectorstore[0m:[36mload[0m:[36m231[0m | [1mLoaded vector store with 27 documents[0m
[32m2025-07-08 17:49:03[0m | [1mINFO    [0m | [36msrc.mcp.tools[0m:[36m_initialize_vector_store[0m:[36m46[0m | [1mRAG tools initialized successfully[0m
INFO:     Started server process [10504]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'agent\config\agent_settings.py'. Reloading...
 ollections HTTP/1.1" 200 OK
INFO:     127.0.0.1:2376 - "GET /favicon.ico HTTP/1.1" 404 Not Found
[32m2025-07-08 17:52:55[0m | [1mINFO    [0m | [36msrc.api.main[0m:[36mupload_document[0m:[36m315[0m | [1mAPI upload request: Amen's book in pdf.pdf[0m
[32m2025-07-08 17:52:55[0m | [1mINFO    [0m | [36msrc.mcp.tools[0m:[36madd_document[0m:[36m146[0m | [1mAdding document: C:\Users\<USER>\AppData\Local\Temp\tmp472sa1f2.pdf[0m
[32m2025-07-08 17:52:55[0m | [1mINFO    [0m | [36msrc.processing.pipeline[0m:[36mprocess_file[0m:[36m124[0m | [1mProcessing file: C:\Users\<USER>\AppData\Local\Temp\tmp472sa1f2.pdf[0m

Batches:   0%|          | 0/5 [00:00<?, ?it/s]
Batches:  20%|##        | 1/5 [00:00<00:01,  2.24it/s]
Batches:  40%|####      | 2/5 [00:00<00:01,  2.42it/s]
Batches:  60%|######    | 3/5 [00:01<00:00,  2.46it/s]
Batches:  80%|########  | 4/5 [00:01<00:00,  2.41it/s]
Batches: 100%|##########| 5/5 [00:01<00:00,  2.85it/s]
[32m2025-07-08 17:53:00[0m | [1mINFO    [0m | [36msrc.core.vectorstore[0m:[36madd_documents[0m:[36m101[0m | [1mAdded 137 documents to vector store[0m
[32m2025-07-08 17:53:00[0m | [1mINFO    [0m | [36msrc.processing.pipeline[0m:[36mprocess_file[0m:[36m168[0m | [1mSuccessfully processed C:\Users\<USER>\AppData\Local\Temp\tmp472sa1f2.pdf: 137 chunks[0m
[32m2025-07-08 17:53:00[0m | [1mINFO    [0m | [36msrc.core.vectorstore[0m:[36msave[0m:[36m196[0m | [1mSaved vector store to C:\Users\<USER>\dev\RAG\data\vectorstore\rag_documents[0m
[32m2025-07-08 17:53:00[0m | [1mINFO    [0m | [36msrc.mcp.tools[0m:[36madd_document[0m:[36m168[0m | [1mDocument added and vector store saved[0m
INFO:     127.0.0.1:2756 - "POST /api/upload HTTP/1.1" 200 OK
INFO:     127.0.0.1:2756 - "GET /api/collections HTTP/1.1" 200 OK
INFO:     127.0.0.1:2756 - "GET /api/collections HTTP/1.1" 200 OK
[32m2025-07-08 17:53:28[0m | [1mINFO    [0m | [36msrc.api.main[0m:[36msearch_documents[0m:[36m288[0m | [1mAPI search request: How should I deal with burnout?[0m
[32m2025-07-08 17:53:28[0m | [1mINFO    [0m | [36msrc.mcp.tools[0m:[36msearch_documents[0m:[36m68[0m | [1mSearching documents: 'How should I deal with burnout?' (type=semantic, k=5)[0m
[32m2025-07-08 17:53:28[0m | [1mINFO    [0m | [36msrc.mcp.tools[0m:[36msearch_documents[0m:[36m85[0m | [1mFound 0 results for query[0m
INFO:     127.0.0.1:2757 - "POST /api/search HTTP/1.1" 200 OK
[32m2025-07-08 17:53:34[0m | [1mINFO    [0m | [36msrc.api.main[0m:[36msearch_documents[0m:[36m288[0m | [1mAPI search request: How should I deal with burnout?[0m
[32m2025-07-08 17:53:34[0m | [1mINFO    [0m | [36msrc.mcp.tools[0m:[36msearch_documents[0m:[36m68[0m | [1mSearching documents: 'How should I deal with burnout?' (type=hybrid, k=5)[0m
[32m2025-07-08 17:53:34[0m | [1mINFO    [0m | [36msrc.mcp.tools[0m:[36msearch_documents[0m:[36m85[0m | [1mFound 5 results for query[0m
INFO:     127.0.0.1:2871 - "POST /api/search HTTP/1.1" 200 OK
INFO:     127.0.0.1:5318 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:5318 - "GET /api/collections HTTP/1.1" 200 OK
INFO:     127.0.0.1:5318 - "GET /api/collections HTTP/1.1" 200 OK
