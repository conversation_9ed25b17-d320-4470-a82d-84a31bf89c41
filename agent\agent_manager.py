#!/usr/bin/env python3
"""
Management script for the Personal Assistant Agent.
"""
import os
import sys
import time
import signal
import subprocess
from pathlib import Path
from typing import Optional, Dict, Any

import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from loguru import logger

from config.agent_settings import settings

app = typer.Typer(help="Personal Assistant Agent Management")
console = Console()


class AgentManager:
    """Manages the Personal Assistant Agent services."""
    
    def __init__(self):
        """Initialize the agent manager."""
        self.agent_root = Path(__file__).parent
        self.logs_dir = settings.logs_dir
        self.logs_dir.mkdir(exist_ok=True)
        
        # Service definitions
        self.services = {
            "web": {
                "command": [sys.executable, "-m", "uvicorn", "src.interfaces.web:app", 
                           "--host", "localhost", "--port", str(settings.web_port)],
                "log_file": self.logs_dir / "web_interface.log",
                "pid_file": self.logs_dir / "web_interface.pid",
                "description": "Web Interface"
            }
        }
    
    def start_service(self, service_name: str, command: list, log_file: Path) -> int:
        """Start a service and return its PID."""
        try:
            # Start the process
            with open(log_file, "w") as f:
                process = subprocess.Popen(
                    command,
                    cwd=self.agent_root,
                    stdout=f,
                    stderr=subprocess.STDOUT,
                    start_new_session=True
                )
            
            # Wait a moment to ensure it started
            time.sleep(2)
            
            # Check if process is still running
            if process.poll() is None:
                return process.pid
            else:
                raise RuntimeError(f"Process exited immediately with code {process.returncode}")
                
        except Exception as e:
            raise RuntimeError(f"Failed to start {service_name}: {e}")
    
    def save_pid(self, service_name: str, pid: int):
        """Save PID to file."""
        pid_file = self.services[service_name]["pid_file"]
        with open(pid_file, "w") as f:
            f.write(str(pid))
    
    def load_pid(self, service_name: str) -> Optional[int]:
        """Load PID from file."""
        pid_file = self.services[service_name]["pid_file"]
        if pid_file.exists():
            try:
                with open(pid_file, "r") as f:
                    return int(f.read().strip())
            except (ValueError, FileNotFoundError):
                return None
        return None
    
    def is_process_running(self, pid: int) -> bool:
        """Check if a process is running."""
        try:
            os.kill(pid, 0)
            return True
        except (OSError, ProcessLookupError):
            return False
    
    def stop_process(self, pid: int, service_name: str) -> bool:
        """Stop a process gracefully."""
        try:
            # Try SIGTERM first
            os.kill(pid, signal.SIGTERM)
            
            # Wait for graceful shutdown
            for _ in range(10):
                if not self.is_process_running(pid):
                    return True
                time.sleep(0.5)
            
            # Force kill if still running
            try:
                os.kill(pid, signal.SIGKILL)
                return True
            except (OSError, ProcessLookupError):
                return True
                
        except (OSError, ProcessLookupError):
            return True  # Process already dead
    
    def get_service_status(self, service_name: str) -> Dict[str, Any]:
        """Get status of a service."""
        pid = self.load_pid(service_name)
        
        if pid is None:
            return {"status": "stopped", "pid": None}
        
        if self.is_process_running(pid):
            return {"status": "running", "pid": pid}
        else:
            # Clean up stale PID file
            pid_file = self.services[service_name]["pid_file"]
            if pid_file.exists():
                pid_file.unlink()
            return {"status": "stopped", "pid": None}
    
    def start_all_services(self) -> bool:
        """Start all agent services."""
        console.print(Panel.fit(
            "[bold blue]Starting Personal Assistant Agent[/bold blue]",
            style="bold blue"
        ))
        
        success = True
        
        # Check prerequisites
        console.print("[cyan]Checking prerequisites...[/cyan]")
        
        # Check if RAG system is available
        rag_check = self._check_rag_system()
        if not rag_check:
            console.print("[yellow]⚠ RAG system not detected - some features may be limited[/yellow]")
        
        # Check Ollama
        ollama_check = self._check_ollama()
        if not ollama_check:
            console.print("[yellow]⚠ Ollama not detected - LLM features may not work[/yellow]")
        
        # Start web interface
        console.print("[cyan]Starting Web Interface...[/cyan]")
        try:
            web_pid = self.start_service(
                "web",
                self.services["web"]["command"],
                self.services["web"]["log_file"]
            )
            self.save_pid("web", web_pid)
            console.print(f"[green]✓[/green] Web Interface started (PID: {web_pid})")
        except Exception as e:
            console.print(f"[red]❌ Failed to start Web Interface: {e}[/red]")
            success = False
        
        if success:
            console.print(Panel.fit(
                "[green]Personal Assistant Agent Started Successfully![/green]\n\n"
                f"🌐 Web Interface: http://localhost:{settings.web_port}\n"
                "💬 CLI Chat: python agent_cli.py chat\n"
                "🛑 Stop: python agent_manager.py stop",
                style="bold green"
            ))
            
            # Try to open browser
            try:
                import webbrowser
                webbrowser.open(f"http://localhost:{settings.web_port}")
            except:
                pass
        
        return success
    
    def stop_all_services(self) -> bool:
        """Stop all agent services."""
        console.print("[yellow]Stopping Personal Assistant Agent...[/yellow]")
        
        success = True
        
        for service_name, service_config in self.services.items():
            status = self.get_service_status(service_name)
            
            if status["status"] == "running":
                console.print(f"[cyan]Stopping {service_config['description']}...[/cyan]")
                
                if self.stop_process(status["pid"], service_name):
                    console.print(f"[green]✓[/green] {service_config['description']} stopped")
                    
                    # Clean up PID file
                    pid_file = service_config["pid_file"]
                    if pid_file.exists():
                        pid_file.unlink()
                else:
                    console.print(f"[red]❌[/red] Failed to stop {service_config['description']}")
                    success = False
            else:
                console.print(f"[yellow]⚠[/yellow] {service_config['description']} was not running")
        
        if success:
            console.print("[green]All services stopped successfully[/green]")
        
        return success
    
    def show_status(self):
        """Show status of all services."""
        status_table = Table(title="Personal Assistant Agent Status")
        status_table.add_column("Service", style="cyan")
        status_table.add_column("Status", style="white")
        status_table.add_column("PID", style="yellow")
        status_table.add_column("Port", style="green")
        
        for service_name, service_config in self.services.items():
            status = self.get_service_status(service_name)
            
            status_text = "🟢 Running" if status["status"] == "running" else "🔴 Stopped"
            pid_text = str(status["pid"]) if status["pid"] else "-"
            
            # Get port from service config
            port = ""
            if service_name == "web":
                port = str(settings.web_port)
            
            status_table.add_row(
                service_config["description"],
                status_text,
                pid_text,
                port
            )
        
        console.print(status_table)
        
        # Show additional info
        console.print("\n[bold]System Information:[/bold]")
        info_table = Table()
        info_table.add_column("Component", style="cyan")
        info_table.add_column("Status", style="white")
        
        info_table.add_row("RAG System", "✓ Available" if self._check_rag_system() else "❌ Not found")
        info_table.add_row("Ollama", "✓ Available" if self._check_ollama() else "❌ Not found")
        info_table.add_row("Data Directory", str(settings.data_dir))
        info_table.add_row("Logs Directory", str(settings.logs_dir))
        
        console.print(info_table)
    
    def _check_rag_system(self) -> bool:
        """Check if RAG system is available."""
        rag_path = Path("../src/mcp/server.py")
        return rag_path.exists()
    
    def _check_ollama(self) -> bool:
        """Check if Ollama is available."""
        try:
            result = subprocess.run(
                ["ollama", "--version"], 
                capture_output=True, 
                timeout=5
            )
            return result.returncode == 0
        except:
            return False


# CLI Commands
@app.command()
def start():
    """Start the Personal Assistant Agent."""
    manager = AgentManager()
    success = manager.start_all_services()
    if not success:
        raise typer.Exit(1)


@app.command()
def stop():
    """Stop the Personal Assistant Agent."""
    manager = AgentManager()
    success = manager.stop_all_services()
    if not success:
        raise typer.Exit(1)


@app.command()
def restart():
    """Restart the Personal Assistant Agent."""
    manager = AgentManager()
    console.print("[yellow]Restarting Personal Assistant Agent...[/yellow]")
    
    manager.stop_all_services()
    time.sleep(2)
    success = manager.start_all_services()
    
    if not success:
        raise typer.Exit(1)


@app.command()
def status():
    """Show the status of all services."""
    manager = AgentManager()
    manager.show_status()


@app.command()
def logs(service: str = typer.Argument("web", help="Service to show logs for")):
    """Show logs for a service."""
    manager = AgentManager()
    
    if service not in manager.services:
        console.print(f"[red]Unknown service: {service}[/red]")
        console.print(f"Available services: {', '.join(manager.services.keys())}")
        raise typer.Exit(1)
    
    log_file = manager.services[service]["log_file"]
    
    if not log_file.exists():
        console.print(f"[yellow]No log file found for {service}[/yellow]")
        return
    
    console.print(f"[cyan]Showing logs for {service}:[/cyan]")
    console.print(f"[dim]Log file: {log_file}[/dim]\n")
    
    try:
        with open(log_file, "r") as f:
            console.print(f.read())
    except Exception as e:
        console.print(f"[red]Error reading log file: {e}[/red]")


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sys.stderr,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan> - <level>{message}</level>"
    )
    
    app()
