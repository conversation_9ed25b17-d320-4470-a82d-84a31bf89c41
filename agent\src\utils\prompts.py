"""
Prompt management for the Personal Assistant Agent.
"""
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

from ...config.agent_settings import settings


class PromptManager:
    """Manages prompts and prompt templates for the agent."""
    
    def __init__(self):
        """Initialize the prompt manager."""
        self.prompts_dir = Path(settings.agent_root) / "config" / "prompts"
        self.prompts_dir.mkdir(exist_ok=True)
        
        # Load or create default prompts
        self._load_default_prompts()
    
    def _load_default_prompts(self):
        """Load default prompt templates."""
        self.system_prompt = """You are a helpful personal assistant with access to a knowledge base. Your role is to:

1. Answer questions accurately using available information
2. Provide helpful and relevant responses
3. Cite sources when using information from documents
4. Maintain conversation context
5. Be concise but thorough

Personality: {personality}
Current time: {current_time}

Guidelines:
- Always be helpful and respectful
- Use information from the knowledge base when relevant
- If you don't know something, say so honestly
- Provide citations when using document information
- Keep responses focused and relevant"""

        self.response_prompt = """Based on the conversation context and available information, provide a helpful response to the user's message.

## Conversation Context:
{conversation_context}

## Available Knowledge:
{knowledge_context}

## User Message:
{user_input}

## Instructions:
- Provide a helpful, accurate response
- Use information from the knowledge base when relevant
- Include citations for document sources: [Source: document_name]
- Maintain conversation flow and context
- Be concise but complete

Response:"""

        self.rag_context_prompt = """## Relevant Information from Knowledge Base:

{rag_results}

This information may be relevant to answering the user's question. Use it if applicable, and cite the sources."""

        self.memory_context_prompt = """## Relevant Context from Previous Interactions:

{memory_items}

This context from previous conversations may be relevant."""

        self.citation_format = "[Source: {source}]"
    
    def build_response_prompt(self, user_input: str, 
                            conversation_context: List[Dict[str, Any]] = None,
                            rag_context: List[Dict[str, Any]] = None,
                            memory_context: List[Dict[str, Any]] = None) -> str:
        """Build a complete prompt for response generation."""
        
        # Build conversation context
        context_text = self._format_conversation_context(conversation_context or [])
        
        # Build knowledge context
        knowledge_text = ""
        
        # Add RAG context
        if rag_context:
            rag_text = self._format_rag_context(rag_context)
            knowledge_text += self.rag_context_prompt.format(rag_results=rag_text) + "\n\n"
        
        # Add memory context
        if memory_context:
            memory_text = self._format_memory_context(memory_context)
            knowledge_text += self.memory_context_prompt.format(memory_items=memory_text) + "\n\n"
        
        if not knowledge_text.strip():
            knowledge_text = "No specific knowledge base information available for this query."
        
        # Build the complete prompt
        prompt = self.response_prompt.format(
            conversation_context=context_text,
            knowledge_context=knowledge_text.strip(),
            user_input=user_input
        )
        
        return prompt
    
    def build_system_prompt(self) -> str:
        """Build the system prompt."""
        return self.system_prompt.format(
            personality=settings.agent_personality,
            current_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
    
    def _format_conversation_context(self, messages: List[Dict[str, Any]]) -> str:
        """Format conversation context for the prompt."""
        if not messages:
            return "This is the start of a new conversation."
        
        context_lines = []
        for msg in messages[-10:]:  # Last 10 messages
            role = msg.get('role', 'unknown')
            content = msg.get('content', '')
            timestamp = msg.get('timestamp', '')
            
            if role == 'user':
                context_lines.append(f"User: {content}")
            elif role == 'assistant':
                context_lines.append(f"Assistant: {content}")
            elif role == 'system':
                context_lines.append(f"System: {content}")
        
        return "\n".join(context_lines) if context_lines else "No previous conversation context."
    
    def _format_rag_context(self, rag_results: List[Dict[str, Any]]) -> str:
        """Format RAG search results for the prompt."""
        if not rag_results:
            return "No relevant documents found."
        
        formatted_results = []
        for i, result in enumerate(rag_results[:5], 1):  # Top 5 results
            doc_id = result.get('doc_id', 'unknown')
            content = result.get('content', '')
            score = result.get('score', 0.0)
            metadata = result.get('metadata', {})
            
            # Get document name from metadata or doc_id
            doc_name = metadata.get('filename', metadata.get('title', doc_id))
            
            # Truncate content if too long
            if len(content) > 500:
                content = content[:500] + "..."
            
            formatted_results.append(
                f"{i}. **{doc_name}** (relevance: {score:.2f})\n"
                f"   {content}\n"
            )
        
        return "\n".join(formatted_results)
    
    def _format_memory_context(self, memory_items: List[Dict[str, Any]]) -> str:
        """Format memory context for the prompt."""
        if not memory_items:
            return "No relevant previous context found."
        
        formatted_items = []
        for i, item in enumerate(memory_items[:3], 1):  # Top 3 memory items
            content = item.get('content', '')
            memory_type = item.get('memory_type', 'unknown')
            importance = item.get('importance', 0.0)
            
            # Truncate content if too long
            if len(content) > 200:
                content = content[:200] + "..."
            
            formatted_items.append(
                f"{i}. **{memory_type.title()}** (importance: {importance:.1f})\n"
                f"   {content}\n"
            )
        
        return "\n".join(formatted_items)
    
    def format_citation(self, source: str) -> str:
        """Format a citation for a source."""
        return self.citation_format.format(source=source)
    
    def extract_citations_from_response(self, response: str) -> List[str]:
        """Extract citations from a response."""
        import re
        
        # Find all citations in the format [Source: document_name]
        citation_pattern = r'\[Source:\s*([^\]]+)\]'
        matches = re.findall(citation_pattern, response)
        
        return [match.strip() for match in matches]
    
    def build_chat_messages(self, user_input: str,
                          conversation_context: List[Dict[str, Any]] = None,
                          rag_context: List[Dict[str, Any]] = None,
                          memory_context: List[Dict[str, Any]] = None) -> List[Dict[str, str]]:
        """Build chat messages for chat completion API."""
        
        messages = []
        
        # Add system message
        messages.append({
            "role": "system",
            "content": self.build_system_prompt()
        })
        
        # Add conversation history
        if conversation_context:
            for msg in conversation_context[-10:]:  # Last 10 messages
                role = msg.get('role', '')
                content = msg.get('content', '')
                
                if role in ['user', 'assistant'] and content:
                    messages.append({
                        "role": role,
                        "content": content
                    })
        
        # Build context for current message
        context_parts = []
        
        # Add RAG context
        if rag_context:
            rag_text = self._format_rag_context(rag_context)
            context_parts.append(f"## Relevant Information:\n{rag_text}")
        
        # Add memory context
        if memory_context:
            memory_text = self._format_memory_context(memory_context)
            context_parts.append(f"## Previous Context:\n{memory_text}")
        
        # Combine user input with context
        if context_parts:
            full_user_message = f"{user_input}\n\n" + "\n\n".join(context_parts)
        else:
            full_user_message = user_input
        
        messages.append({
            "role": "user",
            "content": full_user_message
        })
        
        return messages
    
    def get_prompt_template(self, template_name: str) -> Optional[str]:
        """Get a specific prompt template."""
        templates = {
            'system': self.system_prompt,
            'response': self.response_prompt,
            'rag_context': self.rag_context_prompt,
            'memory_context': self.memory_context_prompt
        }
        
        return templates.get(template_name)
    
    def update_prompt_template(self, template_name: str, template: str) -> bool:
        """Update a prompt template."""
        try:
            if template_name == 'system':
                self.system_prompt = template
            elif template_name == 'response':
                self.response_prompt = template
            elif template_name == 'rag_context':
                self.rag_context_prompt = template
            elif template_name == 'memory_context':
                self.memory_context_prompt = template
            else:
                return False
            
            return True
            
        except Exception:
            return False
