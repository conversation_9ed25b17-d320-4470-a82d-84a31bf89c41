# RAG System - Quick Start Guide

## 🚀 Easy Start/Stop Commands

The RAG system now includes multiple easy-to-use methods for starting and stopping services:

### Option 1: Python Manager (Recommended - Cross-Platform)

```bash
# Start all services
python rag_manager.py start

# Stop all services  
python rag_manager.py stop

# Check status
python rag_manager.py status

# Restart services
python rag_manager.py restart

# View logs
python rag_manager.py logs
python rag_manager.py logs web
python rag_manager.py logs mcp
```

### Option 2: Windows Batch Files

**Double-click or run from command prompt:**
```cmd
start_rag.bat    # Start services
stop_rag.bat     # Stop services
```

### Option 3: Linux/macOS Shell Scripts

```bash
./start_rag.sh   # Start services
./stop_rag.sh    # Stop services
```

## 📋 What Gets Started

When you start the RAG system, the following services are launched:

1. **Web Interface** (Port 8001)
   - Interactive web UI for document management
   - Search interface with real-time results
   - File upload with drag-and-drop support
   - Access at: http://localhost:8001

2. **MCP Server** (Background)
   - Model Context Protocol server for AI agents
   - 8 standardized tools for document operations
   - JSON-RPC 2.0 protocol support

## 🔧 Service Management

### Check Service Status
```bash
python rag_manager.py status
```

This shows:
- Which services are running
- Process IDs (PIDs)
- Service URLs
- Ollama connection status

### View Service Logs
```bash
# All logs
python rag_manager.py logs

# Specific service logs
python rag_manager.py logs web
python rag_manager.py logs mcp
```

### Restart Services
```bash
python rag_manager.py restart
```

## 🌐 Accessing the System

### Web Interface
- **URL**: http://localhost:8001
- **Features**: 
  - Document upload and management
  - Interactive search with filters
  - Collection statistics
  - Real-time progress indicators

### Command Line Interface
```bash
# Search documents
python cli.py search "your query"

# Add documents
python cli.py add-file "document.pdf"
python cli.py add-dir "documents/" --recursive

# View collection info
python cli.py list-docs
```

### MCP Integration
The MCP server runs automatically and provides these tools:
- `search_documents`
- `get_document`
- `add_document`
- `add_directory`
- `list_collections`
- `delete_document`
- `get_similar_documents`
- `clear_collection`

## 🛠 Troubleshooting

### Services Won't Start
1. **Check virtual environment**:
   ```bash
   python rag_manager.py status
   ```

2. **Recreate environment if needed**:
   ```bash
   python setup.py
   ```

3. **Check port conflicts**:
   - Web interface uses port 8001
   - Make sure no other services are using this port

### Services Won't Stop
1. **Force stop with manager**:
   ```bash
   python rag_manager.py stop
   ```

2. **Manual cleanup** (if needed):
   ```bash
   # Windows
   taskkill /f /im python.exe
   
   # Linux/macOS
   pkill -f "src.api.main"
   pkill -f "src.mcp.server"
   ```

### Check Logs for Issues
```bash
python rag_manager.py logs
```

Common log locations:
- `logs/web_interface.log` - Web service logs
- `logs/mcp_server.log` - MCP server logs

## 📁 File Locations

### Service Files
- `rag_manager.py` - Cross-platform Python manager
- `start_rag.bat` / `stop_rag.bat` - Windows batch files
- `start_rag.sh` / `stop_rag.sh` - Linux/macOS shell scripts

### Data Files
- `data/vectorstore/` - Document embeddings and metadata
- `data/documents/` - Uploaded documents (if using file storage)
- `logs/` - Service logs and process IDs

### Configuration
- `.env` - Environment variables
- `config/settings.py` - System configuration

## 🎯 Quick Usage Examples

### 1. Start System and Add Documents
```bash
# Start services
python rag_manager.py start

# Add some documents
python cli.py add-dir "sample_docs" --recursive

# Search for content
python cli.py search "machine learning" --threshold 0.3
```

### 2. Web Interface Workflow
1. Run `python rag_manager.py start`
2. Open http://localhost:8001 in browser
3. Upload documents via drag-and-drop
4. Search using the web interface
5. View collection statistics

### 3. Stop When Done
```bash
python rag_manager.py stop
```

## 💡 Tips

- **Auto-start**: The web interface automatically opens in your browser
- **Background operation**: Services run in the background, you can close terminal windows
- **Persistent data**: Your documents and embeddings are saved between sessions
- **Multiple interfaces**: Use CLI, web, or MCP simultaneously
- **Log monitoring**: Use `python rag_manager.py logs` to monitor system health

## 🔄 System Updates

When updating the RAG system:
1. Stop services: `python rag_manager.py stop`
2. Update code
3. Restart services: `python rag_manager.py start`

Your document collection and settings are preserved across updates.
