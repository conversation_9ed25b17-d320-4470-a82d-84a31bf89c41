@echo off
REM RAG System Stop Script for Windows
REM This script stops all RAG system services

echo.
echo ========================================
echo   Stopping RAG System Services
echo ========================================
echo.

echo [1/3] Stopping RAG Web Interface...
REM Kill processes running on port 8001 (Web Interface)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8001') do (
    taskkill /f /pid %%a >nul 2>&1
)

echo [2/3] Stopping MCP Server...
REM Kill Python processes that might be running MCP server
taskkill /f /im python.exe /fi "WINDOWTITLE eq RAG MCP Server*" >nul 2>&1

echo [3/3] Cleaning up processes...
REM Kill any remaining uvicorn processes
taskkill /f /im uvicorn.exe >nul 2>&1

REM Kill any Python processes with RAG-related command lines
wmic process where "name='python.exe' and commandline like '%%src.api.main%%'" delete >nul 2>&1
wmic process where "name='python.exe' and commandline like '%%src.mcp.server%%'" delete >nul 2>&1

echo.
echo ========================================
echo   RAG System Stopped Successfully!
echo ========================================
echo.
echo All RAG services have been terminated.
echo You can restart the system by running start_rag.bat
echo.
pause
