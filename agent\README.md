# Personal Assistant Agent

A sophisticated personal assistant agent that integrates with your RAG (Retrieval-Augmented Generation) system and local Ollama models to provide intelligent, context-aware responses based on your personal knowledge base.

## 🎯 Features

### Core Capabilities
- **RAG Integration**: Seamlessly connects to your existing RAG system via MCP (Model Context Protocol)
- **Local LLM**: Uses local Ollama models (DeepSeek-R1) for privacy and control
- **Conversation Management**: Persistent conversation history with context awareness
- **Memory System**: Long-term memory for facts, preferences, and context
- **Multi-Interface**: CLI, Web, and API interfaces

### Intelligence Features
- **Context-Aware Responses**: Maintains conversation context across sessions
- **Knowledge Retrieval**: Automatically searches your knowledge base for relevant information
- **Citation Support**: Provides source citations for information from documents
- **Memory Learning**: Learns and remembers user preferences and important facts
- **Streaming Responses**: Real-time response generation with streaming

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    Personal Assistant Agent                     │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   CLI Interface │  │  Web Interface  │  │   API Gateway   │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                      Agent Core Engine                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │ Conversation    │  │ Memory Manager  │  │ Prompt Manager  │  │
│  │ Manager         │  │                 │  │                 │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                    Integration Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐                      │
│  │   MCP Client    │  │ Ollama Client   │                      │
│  │  (RAG System)   │  │ (DeepSeek-R1)   │                      │
│  └─────────────────┘  └─────────────────┘                      │
└─────────────────────────────────────────────────────────────────┘
```

## 🚀 Quick Start

### Prerequisites

1. **RAG System**: Ensure your RAG system is set up and running
2. **Ollama**: Install Ollama and pull the DeepSeek-R1 model
   ```bash
   # Install Ollama from https://ollama.ai/
   ollama pull deepseek-r1
   ```

### Installation

1. **Setup the Agent**:
   ```bash
   cd agent
   python setup_agent.py
   ```

2. **Activate Virtual Environment**:
   ```bash
   # Windows
   venv_agent\Scripts\activate
   
   # Linux/Mac
   source venv_agent/bin/activate
   ```

3. **Configure Settings** (optional):
   ```bash
   cp .env.example .env
   # Edit .env with your preferences
   ```

### Usage

#### Start the Agent
```bash
# Start all services (web interface)
python agent_manager.py start

# Or start individual components
python agent_cli.py chat  # CLI interface only
```

#### Web Interface
- Navigate to http://localhost:8002
- Start chatting with your personal assistant
- Access conversation history and system status

#### CLI Interface
```bash
# Interactive chat
python agent_cli.py chat

# Test the system
python agent_cli.py test

# Show configuration
python agent_cli.py config
```

#### Management
```bash
# Check status
python agent_manager.py status

# Stop services
python agent_manager.py stop

# View logs
python agent_manager.py logs web
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file to customize settings:

```bash
# Agent Settings
AGENT_AGENT_NAME=Assistant
AGENT_AGENT_PERSONALITY=helpful, knowledgeable, and concise

# Ollama Settings
AGENT_OLLAMA_BASE_URL=http://localhost:11434
AGENT_OLLAMA_MODEL=deepseek-r1
AGENT_OLLAMA_TEMPERATURE=0.7

# RAG System Settings
AGENT_RAG_SEARCH_THRESHOLD=0.3
AGENT_MAX_RAG_RESULTS=5

# Interface Settings
AGENT_WEB_PORT=8002
AGENT_API_PORT=8003

# Conversation Settings
AGENT_MAX_CONTEXT_LENGTH=4000
AGENT_CONVERSATION_MEMORY_LIMIT=50
```

### Key Settings

- **`AGENT_OLLAMA_MODEL`**: Ollama model to use (default: deepseek-r1)
- **`AGENT_RAG_SEARCH_THRESHOLD`**: Minimum relevance score for RAG results
- **`AGENT_MAX_RAG_RESULTS`**: Maximum number of documents to retrieve
- **`AGENT_MAX_CONTEXT_LENGTH`**: Maximum conversation context length
- **`AGENT_WEB_PORT`**: Port for web interface

## 📁 Project Structure

```
agent/
├── src/
│   ├── core/
│   │   ├── agent.py              # Main agent orchestrator
│   │   ├── conversation.py       # Conversation management
│   │   └── memory.py            # Memory and context handling
│   ├── integrations/
│   │   ├── mcp_client.py        # MCP client for RAG system
│   │   └── ollama_client.py     # Ollama model interface
│   ├── interfaces/
│   │   └── web.py               # Web interface
│   └── utils/
│       └── prompts.py           # Prompt templates
├── config/
│   └── agent_settings.py        # Configuration
├── data/
│   ├── conversations/           # Conversation history
│   ├── memory/                  # Agent memory storage
│   └── logs/                    # System logs
├── agent_cli.py                # CLI interface
├── agent_manager.py            # Service management
├── test_agent.py              # Test suite
└── setup_agent.py             # Setup script
```

## 🧪 Testing

Run the comprehensive test suite:

```bash
python test_agent.py
```

The test suite covers:
- Core component functionality
- MCP and Ollama integration
- Conversation and memory management
- Performance characteristics
- End-to-end agent functionality

## 🔌 Integration

### With RAG System
The agent automatically connects to your RAG system via MCP. Ensure:
1. RAG system is running
2. MCP server is accessible
3. Path to RAG system is correct in settings

### With Ollama
The agent uses Ollama for LLM capabilities. Ensure:
1. Ollama is installed and running
2. DeepSeek-R1 model is pulled
3. Ollama API is accessible at configured URL

## 🎯 Usage Examples

### Basic Chat
```
You: What is machine learning?
Assistant: Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed. [Source: ai_overview.md]

Based on the information in your knowledge base, machine learning involves algorithms that can identify patterns in data and make predictions or decisions. Would you like me to explain any specific aspect of machine learning?
```

### Context Awareness
```
You: Can you explain neural networks?
Assistant: Neural networks are computing systems inspired by biological neural networks... [detailed explanation with sources]

You: How do they relate to what we discussed earlier?
Assistant: Neural networks are actually a key component of machine learning, which we just discussed. They're one of the main techniques used to implement machine learning algorithms...
```

### Memory Integration
```
You: I prefer Python for programming
Assistant: I'll remember that you prefer Python for programming. This will help me provide more relevant examples and suggestions in the future.

[Later conversation]
You: What's a good way to implement this algorithm?
Assistant: Since you prefer Python, here's a Python implementation... [tailored response]
```

## 🛠️ Development

### Adding New Features
1. Core functionality goes in `src/core/`
2. Integrations go in `src/integrations/`
3. Interfaces go in `src/interfaces/`
4. Update tests in `test_agent.py`

### Custom Prompts
Modify prompts in `src/utils/prompts.py` or create custom templates in `config/prompts/`.

### Memory Types
The agent supports different memory types:
- **fact**: Factual information
- **preference**: User preferences
- **context**: Conversation context
- **skill**: Learned capabilities

## 🔍 Troubleshooting

### Common Issues

1. **Agent won't start**
   - Check if RAG system is running
   - Verify Ollama is installed and model is pulled
   - Check logs: `python agent_manager.py logs web`

2. **No responses from LLM**
   - Verify Ollama is running: `ollama list`
   - Check model availability: `ollama pull deepseek-r1`
   - Check Ollama URL in configuration

3. **RAG integration not working**
   - Ensure RAG system MCP server is running
   - Check RAG system path in configuration
   - Verify MCP client can connect

4. **Memory/Conversation issues**
   - Check data directory permissions
   - Verify SQLite database is accessible
   - Check disk space

### Logs
View detailed logs for debugging:
```bash
python agent_manager.py logs web
```

## 📈 Performance

- **Response Time**: Sub-second for most queries
- **Memory Usage**: Efficient SQLite-based storage
- **Conversation Context**: Configurable context window
- **Concurrent Users**: Supports multiple simultaneous conversations

## 🔮 Future Enhancements

- Voice interface integration
- Advanced memory consolidation
- Plugin system for custom tools
- Multi-model support
- Enhanced web interface features
- Mobile app integration

## 📄 License

This project is part of the RAG system suite and follows the same licensing terms.

---

**Ready to chat with your personal assistant?** Start with `python agent_manager.py start` and visit http://localhost:8002!
