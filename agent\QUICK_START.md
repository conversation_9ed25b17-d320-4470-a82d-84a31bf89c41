# Personal Assistant Agent - Quick Start Guide

Get your Personal Assistant Agent up and running in minutes!

## 🚀 Super Quick Start

### Windows (Double-Click Method)
1. **Double-click** `start_agent.bat`
2. Wait for setup and startup
3. **Chat at** http://localhost:8002

### Linux/macOS (Terminal Method)
```bash
./start_agent.sh   # Start the agent
./stop_agent.sh    # Stop the agent
```

### Alternative (Cross-Platform)
```bash
cd agent
python agent_manager.py start   # Start services
python agent_manager.py stop    # Stop services
```

## 📋 What Gets Started

When you start the Personal Assistant Agent, the following happens:

1. **Environment Check** 
   - Verifies virtual environment exists (creates if needed)
   - Checks if RAG system is running (starts if needed)
   - Validates Ollama availability

2. **Agent Services**
   - **Web Interface** (Port 8002): Interactive chat interface
   - **Background Services**: Conversation and memory management
   - **Integration Layer**: RAG and Ollama connections

3. **Ready to Use**
   - Web interface opens automatically
   - CLI chat available via `python agent_cli.py chat`
   - All services running in background

## 🎯 Access Methods

### 🌐 Web Interface (Recommended)
- **URL**: http://localhost:8002
- **Features**: Real-time chat, conversation history, responsive design
- **Best for**: General use, visual interface, easy access

### 💻 Command Line Interface
```bash
python agent_cli.py chat      # Interactive chat
python agent_cli.py test      # Test system
python agent_cli.py config    # Show configuration
```
- **Best for**: Power users, scripting, terminal lovers

### 🔧 Management Commands
```bash
python agent_manager.py start     # Start all services
python agent_manager.py stop      # Stop all services
python agent_manager.py status    # Check service status
python agent_manager.py restart   # Restart services
python agent_manager.py logs web  # View logs
```

## ⚙️ Prerequisites

### Required
- **Python 3.8+**: For running the agent
- **RAG System**: Your existing RAG system should be running

### Recommended
- **Ollama**: For local LLM capabilities
  ```bash
  # Install from https://ollama.ai/
  ollama pull deepseek-r1
  ```

### Optional
- **GPU**: For faster processing (automatically detected)

## 🔧 First-Time Setup

If this is your first time running the agent:

1. **Run Setup** (automatic on first start):
   ```bash
   python setup_agent.py
   ```

2. **Configure** (optional):
   ```bash
   cp .env.example .env
   # Edit .env with your preferences
   ```

3. **Test Everything**:
   ```bash
   python test_agent.py
   ```

## 💬 Example Conversations

### Basic Chat
```
You: Hello! How are you?
Assistant: Hello! I'm doing well, thank you for asking. I'm your personal assistant, ready to help you with questions, information from your knowledge base, and various tasks. How can I assist you today?
```

### Knowledge-Based Query
```
You: What is machine learning?
Assistant: Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed. [Source: ai_overview.md]

Based on the information in your knowledge base, machine learning involves algorithms that can identify patterns in data and make predictions or decisions. Would you like me to explain any specific aspect of machine learning?
```

### Context Awareness
```
You: Can you explain neural networks?
Assistant: Neural networks are computing systems inspired by biological neural networks... [detailed explanation]

You: How do they relate to what we discussed earlier?
Assistant: Neural networks are actually a key component of machine learning, which we just discussed. They're one of the main techniques used to implement machine learning algorithms...
```

## 🛠️ Troubleshooting

### Agent Won't Start
1. **Check RAG System**: Ensure your RAG system is running
2. **Check Ollama**: Run `ollama list` to verify installation
3. **Check Logs**: `python agent_manager.py logs web`
4. **Manual Setup**: `python setup_agent.py`

### No Responses
1. **Check Ollama**: `ollama pull deepseek-r1`
2. **Check Model**: Verify model is available
3. **Check Connections**: `python agent_cli.py test`

### Web Interface Issues
1. **Check Port**: Ensure port 8002 is available
2. **Check Browser**: Try http://localhost:8002
3. **Check Firewall**: Allow port 8002 if needed

### Performance Issues
1. **Check GPU**: CUDA availability for faster processing
2. **Check Memory**: Ensure sufficient RAM available
3. **Check Disk**: Ensure sufficient disk space

## 📊 System Status

Check system health anytime:

```bash
python agent_manager.py status
```

This shows:
- ✅ Service status (running/stopped)
- 🔗 Connection status (RAG, Ollama)
- 📈 System metrics
- 📁 Data directories

## 🎛️ Configuration

### Quick Settings
Edit `.env` file for common settings:
```bash
AGENT_AGENT_NAME=MyAssistant
AGENT_OLLAMA_MODEL=deepseek-r1
AGENT_WEB_PORT=8002
AGENT_MAX_CONTEXT_LENGTH=4000
```

### Advanced Settings
See `config/agent_settings.py` for all options.

## 🔄 Updates and Maintenance

### Regular Maintenance
```bash
# Check system health
python test_agent.py

# Clean old conversations (optional)
python agent_cli.py config

# Update Ollama models
ollama pull deepseek-r1
```

### Backup Important Data
- **Conversations**: `data/conversations/`
- **Memory**: `data/memory/`
- **Configuration**: `.env` file

## 🆘 Getting Help

### Built-in Help
```bash
python agent_cli.py --help
python agent_manager.py --help
```

### Web Interface Help
- Type `help` in the chat interface
- Use the status commands for system information

### Common Commands
```bash
# In chat interface
help          # Show available commands
status        # Show agent status
conversations # List recent conversations
memory        # Show memory statistics
quit          # Exit chat
```

## 🎉 You're Ready!

Your Personal Assistant Agent is now ready to:
- 🧠 Answer questions using your knowledge base
- 💬 Maintain context across conversations
- 📚 Learn and remember your preferences
- 🔄 Provide real-time streaming responses
- 🌐 Work across multiple interfaces

**Start chatting**: http://localhost:8002 or `python agent_cli.py chat`

Enjoy your new AI assistant! 🤖✨
