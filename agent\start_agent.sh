#!/bin/bash
# Start script for Personal Assistant Agent (Linux/macOS)

echo "========================================"
echo "   Personal Assistant Agent Startup"
echo "========================================"
echo

# Check if we're in the right directory
if [ ! -f "agent_manager.py" ]; then
    echo "Error: agent_manager.py not found!"
    echo "Please run this script from the agent directory."
    echo
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "venv_agent" ]; then
    echo "Virtual environment not found. Running setup..."
    echo
    python3 setup_agent.py
    if [ $? -ne 0 ]; then
        echo "Setup failed! Please check the errors above."
        exit 1
    fi
    echo
fi

# Activate virtual environment
echo "[1/4] Activating virtual environment..."
source venv_agent/bin/activate
if [ $? -ne 0 ]; then
    echo "Failed to activate virtual environment!"
    exit 1
fi

# Check if RAG system is running
echo "[2/4] Checking RAG system..."
cd ..
python3 -c "import requests; requests.get('http://localhost:8001/api/status', timeout=2)" >/dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "RAG system not detected. Starting RAG system first..."
    if [ -f "start_rag.sh" ]; then
        ./start_rag.sh
        sleep 5
        echo "RAG system started. Continuing with agent..."
    else
        echo "Warning: RAG system start script not found."
        echo "Please ensure the RAG system is running manually."
    fi
else
    echo "RAG system is already running."
fi
cd agent

# Check Ollama
echo "[3/4] Checking Ollama..."
ollama list >/dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "Warning: Ollama not found or not running."
    echo "Please ensure Ollama is installed and running."
    echo "You can download it from: https://ollama.ai/"
    echo
    echo "Continuing anyway - some features may not work..."
else
    echo "Ollama is available."
fi

# Start the agent
echo "[4/4] Starting Personal Assistant Agent..."
echo
echo "Starting agent services..."
echo "Web interface will be available at: http://localhost:8002"
echo

# Start using the agent manager
python3 agent_manager.py start

if [ $? -ne 0 ]; then
    echo
    echo "Failed to start the agent! Check the error messages above."
    exit 1
fi

echo
echo "========================================"
echo "   Personal Assistant Agent Started!"
echo "========================================"
echo
echo "Web Interface: http://localhost:8002"
echo "CLI Chat: python3 agent_cli.py chat"
echo "Status: python3 agent_manager.py status"
echo "Stop: python3 agent_manager.py stop"
echo
echo "The web interface should open automatically."
echo "Press Enter to continue..."
read
