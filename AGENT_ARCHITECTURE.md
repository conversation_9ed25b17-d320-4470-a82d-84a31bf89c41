# Personal Assistant Agent Architecture

## 🎯 Overview

A personal assistant agent that integrates with the existing RAG system via MCP and uses local Ollama models (DeepSeek-R1) to provide intelligent, context-aware responses based on your personal knowledge base.

## 🏗️ System Architecture

### Core Components

```
┌─────────────────────────────────────────────────────────────────┐
│                    Personal Assistant Agent                     │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   CLI Interface │  │  Web Interface  │  │   API Gateway   │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                      Agent Core Engine                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │ Conversation    │  │ Query Analyzer  │  │ Response        │  │
│  │ Manager         │  │                 │  │ Synthesizer     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                    Integration Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   MCP Client    │  │ Ollama Client   │  │ RAG Pipeline    │  │
│  │  (RAG System)   │  │ (DeepSeek-R1)   │  │                 │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    External Services                           │
│  ┌─────────────────┐  ┌─────────────────┐                      │
│  │   RAG System    │  │     Ollama      │                      │
│  │  (MCP Server)   │  │  (DeepSeek-R1)  │                      │
│  └─────────────────┘  └─────────────────┘                      │
└─────────────────────────────────────────────────────────────────┘
```

## 🔄 Conversation Flow

### 1. User Input Processing
```
User Query → Input Validation → Intent Analysis → Context Retrieval
```

### 2. Knowledge Retrieval
```
Query Analysis → RAG Search (via MCP) → Document Retrieval → Context Assembly
```

### 3. Response Generation
```
Context + Query → Prompt Engineering → Ollama LLM → Response Generation → Post-processing
```

### 4. Conversation Management
```
Response → Memory Update → Context Storage → History Management
```

## 📁 Project Structure

```
agent/
├── src/
│   ├── core/
│   │   ├── agent.py              # Main agent orchestrator
│   │   ├── conversation.py       # Conversation management
│   │   ├── memory.py            # Memory and context handling
│   │   └── pipeline.py          # RAG-LLM pipeline
│   ├── integrations/
│   │   ├── mcp_client.py        # MCP client for RAG system
│   │   ├── ollama_client.py     # Ollama model interface
│   │   └── rag_pipeline.py      # RAG integration pipeline
│   ├── interfaces/
│   │   ├── cli.py               # Command-line interface
│   │   ├── web.py               # Web interface
│   │   └── api.py               # REST API
│   └── utils/
│       ├── prompts.py           # Prompt templates
│       ├── validators.py        # Input validation
│       └── formatters.py        # Response formatting
├── config/
│   ├── agent_settings.py        # Agent configuration
│   └── prompts/                 # Prompt templates
├── data/
│   ├── conversations/           # Conversation history
│   └── memory/                  # Agent memory storage
├── tests/
│   ├── test_agent.py           # Agent core tests
│   ├── test_integrations.py    # Integration tests
│   └── test_interfaces.py      # Interface tests
├── agent_manager.py            # Start/stop management
├── agent_cli.py               # CLI entry point
└── requirements_agent.txt     # Agent dependencies
```

## 🔧 Key Features

### 1. Intelligent Query Processing
- **Intent Recognition**: Understand user intent (search, question, task)
- **Context Awareness**: Maintain conversation context
- **Query Optimization**: Optimize queries for RAG retrieval

### 2. RAG Integration
- **MCP Client**: Connect to existing RAG system
- **Smart Retrieval**: Context-aware document retrieval
- **Relevance Filtering**: Filter and rank retrieved content

### 3. LLM Integration
- **Ollama Client**: Interface with local DeepSeek-R1
- **Prompt Engineering**: Optimized prompts for different tasks
- **Response Streaming**: Real-time response generation

### 4. Conversation Management
- **Memory System**: Short-term and long-term memory
- **Context Tracking**: Maintain conversation context
- **History Management**: Persistent conversation history

### 5. Multi-Interface Support
- **CLI Interface**: Command-line interaction
- **Web Interface**: Browser-based chat interface
- **API Gateway**: REST API for integrations

## 🚀 Implementation Plan

### Phase 1: Core Framework
1. Agent core engine
2. Conversation manager
3. Basic MCP client
4. Simple CLI interface

### Phase 2: LLM Integration
1. Ollama client implementation
2. Prompt engineering system
3. Response generation pipeline
4. Context management

### Phase 3: Advanced Features
1. Web interface
2. Memory system
3. Advanced conversation features
4. Performance optimization

### Phase 4: Production Ready
1. Management tools
2. Comprehensive testing
3. Documentation
4. Deployment scripts

## 🔌 Integration Points

### RAG System (via MCP)
- **Tools Used**: search_documents, get_document, get_similar_documents
- **Connection**: JSON-RPC over stdio
- **Data Flow**: Query → MCP → RAG → Results → Agent

### Ollama (DeepSeek-R1)
- **API**: HTTP REST API
- **Models**: deepseek-r1 (primary)
- **Features**: Streaming, context management, temperature control

## 📊 Configuration

### Agent Settings
```python
class AgentSettings:
    # RAG Integration
    mcp_server_command: str = "python -m src.mcp.server"
    rag_search_threshold: float = 0.3
    max_rag_results: int = 5
    
    # Ollama Integration
    ollama_base_url: str = "http://localhost:11434"
    ollama_model: str = "deepseek-r1"
    ollama_temperature: float = 0.7
    
    # Conversation
    max_context_length: int = 4000
    conversation_memory_limit: int = 50
    
    # Interfaces
    cli_port: int = None
    web_port: int = 8002
    api_port: int = 8003
```

## 🎯 Success Metrics

- **Response Quality**: Relevant, accurate, contextual responses
- **Performance**: Sub-second response times
- **Integration**: Seamless RAG and LLM integration
- **Usability**: Intuitive interfaces and easy management
- **Reliability**: Robust error handling and recovery

This architecture provides a solid foundation for building a personal assistant that leverages your existing RAG system while adding intelligent conversation capabilities through local LLM integration.
