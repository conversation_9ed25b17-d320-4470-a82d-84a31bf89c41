@echo off
REM Stop script for Personal Assistant Agent (Windows)
echo ========================================
echo   Personal Assistant Agent Shutdown
echo ========================================
echo.

REM Check if we're in the right directory
if not exist "agent_manager.py" (
    echo Error: agent_manager.py not found!
    echo Please run this script from the agent directory.
    echo.
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv_agent" (
    echo Virtual environment not found.
    echo The agent may not be running.
    echo.
    goto :cleanup
)

REM Activate virtual environment
echo Activating virtual environment...
call venv_agent\Scripts\activate.bat
if errorlevel 1 (
    echo Failed to activate virtual environment!
    echo Attempting manual cleanup...
    goto :cleanup
)

REM Stop the agent using the manager
echo Stopping Personal Assistant Agent...
python agent_manager.py stop

if errorlevel 1 (
    echo Warning: Agent manager reported issues during shutdown.
    echo Attempting manual cleanup...
    goto :cleanup
)

echo.
echo Agent stopped successfully!
goto :end

:cleanup
echo.
echo Performing manual cleanup...

REM Kill any remaining Python processes that might be agent-related
echo Checking for remaining agent processes...

REM Look for processes on agent ports
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":8002"') do (
    echo Terminating process on port 8002 (PID: %%a)
    taskkill /PID %%a /F >nul 2>&1
)

for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":8003"') do (
    echo Terminating process on port 8003 (PID: %%a)
    taskkill /PID %%a /F >nul 2>&1
)

REM Clean up PID files if they exist
if exist "data\logs\web_interface.pid" (
    echo Cleaning up PID files...
    del "data\logs\web_interface.pid" >nul 2>&1
)

if exist "data\logs\api_server.pid" (
    del "data\logs\api_server.pid" >nul 2>&1
)

echo Manual cleanup completed.

:end
echo.
echo ========================================
echo   Personal Assistant Agent Stopped
echo ========================================
echo.
echo All agent services have been terminated.
echo You can restart the agent with: start_agent.bat
echo.
pause
