"""
Conversation management for the Personal Assistant Agent.
"""
import json
import uuid
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from loguru import logger

from ...config.agent_settings import settings


@dataclass
class Message:
    """Represents a single message in a conversation."""
    id: str
    role: str  # 'user', 'assistant', 'system'
    content: str
    timestamp: datetime
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary."""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """Create message from dictionary."""
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        return cls(**data)


@dataclass
class Conversation:
    """Represents a conversation session."""
    id: str
    title: str
    messages: List[Message]
    created_at: datetime
    updated_at: datetime
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    def add_message(self, role: str, content: str, metadata: Dict[str, Any] = None) -> Message:
        """Add a message to the conversation."""
        message = Message(
            id=str(uuid.uuid4()),
            role=role,
            content=content,
            timestamp=datetime.now(),
            metadata=metadata or {}
        )
        self.messages.append(message)
        self.updated_at = datetime.now()
        return message
    
    def get_recent_messages(self, limit: int = None) -> List[Message]:
        """Get recent messages, optionally limited."""
        if limit is None:
            limit = settings.conversation_memory_limit
        return self.messages[-limit:] if limit > 0 else self.messages
    
    def get_context_length(self) -> int:
        """Calculate total context length in characters."""
        return sum(len(msg.content) for msg in self.messages)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert conversation to dictionary."""
        return {
            'id': self.id,
            'title': self.title,
            'messages': [msg.to_dict() for msg in self.messages],
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Conversation':
        """Create conversation from dictionary."""
        messages = [Message.from_dict(msg_data) for msg_data in data['messages']]
        return cls(
            id=data['id'],
            title=data['title'],
            messages=messages,
            created_at=datetime.fromisoformat(data['created_at']),
            updated_at=datetime.fromisoformat(data['updated_at']),
            metadata=data.get('metadata', {})
        )


class ConversationManager:
    """Manages conversations and their persistence."""
    
    def __init__(self):
        """Initialize the conversation manager."""
        self.current_conversation: Optional[Conversation] = None
        self.conversations_dir = settings.conversations_dir
        self.conversations_dir.mkdir(exist_ok=True)
    
    def create_conversation(self, title: str = None) -> Conversation:
        """Create a new conversation."""
        if title is None:
            title = f"Conversation {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        
        conversation = Conversation(
            id=str(uuid.uuid4()),
            title=title,
            messages=[],
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        self.current_conversation = conversation
        logger.info(f"Created new conversation: {conversation.id}")
        return conversation
    
    def load_conversation(self, conversation_id: str) -> Optional[Conversation]:
        """Load a conversation by ID."""
        file_path = self.conversations_dir / f"{conversation_id}.json"
        
        if not file_path.exists():
            logger.warning(f"Conversation file not found: {file_path}")
            return None
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            conversation = Conversation.from_dict(data)
            self.current_conversation = conversation
            logger.info(f"Loaded conversation: {conversation_id}")
            return conversation
            
        except Exception as e:
            logger.error(f"Failed to load conversation {conversation_id}: {e}")
            return None
    
    def save_conversation(self, conversation: Conversation = None) -> bool:
        """Save a conversation to disk."""
        if conversation is None:
            conversation = self.current_conversation
        
        if conversation is None:
            logger.warning("No conversation to save")
            return False
        
        file_path = self.conversations_dir / f"{conversation.id}.json"
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(conversation.to_dict(), f, indent=2, ensure_ascii=False)
            
            logger.debug(f"Saved conversation: {conversation.id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save conversation {conversation.id}: {e}")
            return False
    
    def list_conversations(self, limit: int = 50) -> List[Dict[str, Any]]:
        """List recent conversations."""
        conversations = []
        
        for file_path in self.conversations_dir.glob("*.json"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                conversations.append({
                    'id': data['id'],
                    'title': data['title'],
                    'created_at': data['created_at'],
                    'updated_at': data['updated_at'],
                    'message_count': len(data['messages'])
                })
                
            except Exception as e:
                logger.warning(f"Failed to read conversation file {file_path}: {e}")
        
        # Sort by updated_at descending
        conversations.sort(key=lambda x: x['updated_at'], reverse=True)
        return conversations[:limit]
    
    def delete_conversation(self, conversation_id: str) -> bool:
        """Delete a conversation."""
        file_path = self.conversations_dir / f"{conversation_id}.json"
        
        try:
            if file_path.exists():
                file_path.unlink()
                logger.info(f"Deleted conversation: {conversation_id}")
                
                # Clear current conversation if it's the one being deleted
                if (self.current_conversation and 
                    self.current_conversation.id == conversation_id):
                    self.current_conversation = None
                
                return True
            else:
                logger.warning(f"Conversation file not found: {conversation_id}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to delete conversation {conversation_id}: {e}")
            return False
    
    def cleanup_old_conversations(self) -> int:
        """Clean up conversations older than the retention period."""
        cutoff_date = datetime.now() - timedelta(days=settings.conversation_history_days)
        deleted_count = 0
        
        for file_path in self.conversations_dir.glob("*.json"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                updated_at = datetime.fromisoformat(data['updated_at'])
                if updated_at < cutoff_date:
                    file_path.unlink()
                    deleted_count += 1
                    logger.debug(f"Deleted old conversation: {data['id']}")
                    
            except Exception as e:
                logger.warning(f"Failed to process conversation file {file_path}: {e}")
        
        if deleted_count > 0:
            logger.info(f"Cleaned up {deleted_count} old conversations")
        
        return deleted_count
    
    def add_message(self, role: str, content: str, metadata: Dict[str, Any] = None) -> Message:
        """Add a message to the current conversation."""
        if self.current_conversation is None:
            self.create_conversation()
        
        message = self.current_conversation.add_message(role, content, metadata)
        
        # Auto-save if enabled
        if settings.auto_save_conversations:
            self.save_conversation()
        
        return message
    
    def get_current_conversation(self) -> Optional[Conversation]:
        """Get the current conversation."""
        return self.current_conversation
    
    def get_conversation_context(self, max_length: int = None) -> List[Message]:
        """Get conversation context for LLM."""
        if self.current_conversation is None:
            return []
        
        if max_length is None:
            max_length = settings.max_context_length
        
        messages = self.current_conversation.get_recent_messages()
        
        # Trim messages to fit within context length
        total_length = 0
        context_messages = []
        
        for message in reversed(messages):
            message_length = len(message.content)
            if total_length + message_length > max_length:
                break
            
            context_messages.insert(0, message)
            total_length += message_length
        
        return context_messages
