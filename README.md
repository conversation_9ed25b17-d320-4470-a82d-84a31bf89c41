# RAG System with MCP Integration

A comprehensive Retrieval-Augmented Generation (RAG) system with Model Context Protocol (MCP) integration for standardized access by AI agents.

## Features

- **Multi-format Document Support**: PDF, DOCX, HTML, TXT, MD, and more
- **GPU-Accelerated Embeddings**: Using sentence-transformers with CUDA support
- **FAISS Vector Storage**: Efficient similarity search and persistence
- **Flexible Chunking**: Recursive and semantic text chunking strategies
- **MCP Integration**: Standardized tools for AI agent access
- **Hybrid Search**: Semantic and keyword-based search capabilities
- **CLI Interface**: Easy-to-use command-line tools
- **Async Processing**: Efficient batch processing capabilities

## Architecture

The system consists of several key components:

1. **Document Processing Pipeline**: Extracts text from various file formats
2. **Embedding Generation**: Converts text to vector representations
3. **Vector Storage**: FAISS-based storage with metadata support
4. **Retrieval Engine**: Semantic and hybrid search capabilities
5. **MCP Server**: Standardized interface for AI agents
6. **CLI Tools**: Command-line interface for management

## Installation

### Prerequisites

- Python 3.11+
- NVIDIA GPU (optional, for CUDA acceleration)
- Ollama (for local LLM inference)

### Setup

1. **Clone and setup the environment:**
   ```bash
   git clone <repository-url>
   cd RAG
   python setup.py
   ```

2. **Activate the virtual environment:**
   ```bash
   # Windows
   venv\Scripts\activate
   
   # Linux/Mac
   source venv/bin/activate
   ```

3. **Install and configure Ollama:**
   ```bash
   # Download from https://ollama.ai/
   ollama pull deepseek-r1
   ```

## Quick Start

### Easy Start/Stop (Recommended)

```bash
# Start all services
python rag_manager.py start

# Check status
python rag_manager.py status

# Stop all services
python rag_manager.py stop
```

**Alternative methods:**
- Windows: Double-click `start_rag.bat` / `stop_rag.bat`
- Linux/macOS: `./start_rag.sh` / `./stop_rag.sh`

Once started, access the web interface at: **http://localhost:8001**

📖 **See [QUICK_START.md](QUICK_START.md) for detailed instructions**

## Usage

### Command Line Interface

The system includes a comprehensive CLI for document management and search:

#### Add Documents

```bash
# Add a single file
python cli.py add-file "path/to/document.pdf"

# Add all files from a directory
python cli.py add-dir "path/to/documents" --recursive

# Add with specific chunking strategy
python cli.py add-file "document.txt" --chunking semantic
```

#### Search Documents

```bash
# Basic semantic search
python cli.py search "your search query"

# Hybrid search with custom parameters
python cli.py search "query" --type hybrid --count 10 --threshold 0.8
```

#### Manage Collections

```bash
# List collections and statistics
python cli.py list-docs

# Get specific document
python cli.py get-doc "document_id_here"

# Find similar documents
python cli.py similar "document_id_here" --count 5

# Clear all documents
python cli.py clear
```

### MCP Server

Start the MCP server for AI agent integration:

```bash
python -m src.mcp.server
```

#### Available MCP Tools

- `search_documents`: Search for relevant documents
- `get_document`: Retrieve specific document by ID
- `add_document`: Add a document to the system
- `add_directory`: Add all documents from a directory
- `list_collections`: List collections and statistics
- `delete_document`: Delete a document
- `get_similar_documents`: Find similar documents
- `clear_collection`: Clear all documents

### Python API

```python
from src.mcp.tools import RAGTools

# Initialize RAG tools
rag = RAGTools()

# Add a document
result = rag.add_document("path/to/document.pdf")

# Search documents
results = rag.search_documents("your query", k=5)

# Get document by ID
doc = rag.get_document("document_id")
```

## Configuration

Configuration is managed through environment variables and the `.env` file:

```env
# LLM Configuration
OLLAMA_HOST=http://localhost:11434
DEFAULT_MODEL=deepseek-r1:latest

# Embedding Configuration
EMBEDDING_DEVICE=cuda
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2

# Processing Configuration
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_FILE_SIZE_MB=100

# Vector Database
VECTORSTORE_TYPE=faiss
COLLECTION_NAME=rag_documents
```

## Supported File Types

- **Text**: .txt, .md, .rst, .log, .csv, .json, .xml, .yaml, .yml
- **PDF**: .pdf
- **Microsoft Office**: .docx
- **Web**: .html, .htm

## Development

### Project Structure

```
RAG/
├── src/
│   ├── core/              # Core RAG components
│   │   ├── embeddings.py  # Embedding generation
│   │   ├── vectorstore.py # Vector storage
│   │   └── retrieval.py   # Search and retrieval
│   ├── processing/        # Document processing
│   │   ├── extractors/    # File format extractors
│   │   ├── chunking.py    # Text chunking
│   │   └── pipeline.py    # Processing pipeline
│   └── mcp/              # MCP integration
│       ├── server.py     # MCP server
│       └── tools.py      # MCP tools
├── config/               # Configuration
├── data/                # Data storage
├── cli.py               # Command-line interface
└── requirements.txt     # Dependencies
```

### Testing

```bash
# Run tests
pytest tests/

# Test with a sample document
python cli.py add-file "README.md"
python cli.py search "RAG system"
```

## Performance Optimization

- **GPU Acceleration**: Automatically uses CUDA if available
- **Batch Processing**: Efficient processing of multiple documents
- **Persistent Storage**: Vector store persists between sessions
- **Async Operations**: Non-blocking operations for better performance

## Troubleshooting

### Common Issues

1. **CUDA not available**: System falls back to CPU automatically
2. **Memory issues**: Reduce batch size or chunk size in configuration
3. **File format errors**: Check if file type is supported
4. **Ollama connection**: Ensure Ollama is running on correct port

### Logs

Logs are stored in `data/logs/rag_system.log` with configurable levels.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Acknowledgments

- **sentence-transformers**: For embedding generation
- **FAISS**: For efficient vector search
- **MCP**: For standardized AI agent integration
- **Ollama**: For local LLM inference
