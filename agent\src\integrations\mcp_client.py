"""
MCP client for connecting to the RAG system.
"""
import asyncio
import json
import subprocess
from typing import Dict, Any, List, Optional
from pathlib import Path
from loguru import logger

from ...config.agent_settings import settings


class MCPClient:
    """Client for communicating with the RAG system's MCP server."""
    
    def __init__(self):
        """Initialize the MCP client."""
        self.process: Optional[subprocess.Popen] = None
        self._connected = False
        self._request_id = 0
    
    async def initialize(self) -> bool:
        """Initialize the MCP connection."""
        try:
            # Start the MCP server process
            logger.info("Starting RAG MCP server...")
            
            # Get the working directory for the RAG system
            rag_cwd = Path(settings.agent_root) / settings.rag_mcp_cwd
            rag_cwd = rag_cwd.resolve()
            
            if not rag_cwd.exists():
                logger.error(f"RAG system directory not found: {rag_cwd}")
                return False
            
            # Start the MCP server process
            self.process = subprocess.Popen(
                [
                    "python", "-m", "src.mcp.server"
                ],
                cwd=str(rag_cwd),
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=0
            )
            
            # Wait a moment for the server to start
            await asyncio.sleep(2)
            
            # Test the connection
            if await self._test_connection():
                self._connected = True
                logger.info("MCP client connected successfully")
                return True
            else:
                logger.error("MCP connection test failed")
                await self.cleanup()
                return False
                
        except Exception as e:
            logger.error(f"Failed to initialize MCP client: {e}")
            await self.cleanup()
            return False
    
    async def _test_connection(self) -> bool:
        """Test the MCP connection."""
        try:
            result = await self.list_collections()
            return result is not None
        except Exception as e:
            logger.error(f"MCP connection test failed: {e}")
            return False
    
    async def _send_request(self, method: str, params: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """Send a request to the MCP server."""
        if not self.process or not self._connected:
            logger.error("MCP client not connected")
            return None
        
        try:
            self._request_id += 1
            request = {
                "jsonrpc": "2.0",
                "id": self._request_id,
                "method": method,
                "params": params or {}
            }
            
            # Send request
            request_json = json.dumps(request) + "\n"
            self.process.stdin.write(request_json)
            self.process.stdin.flush()
            
            # Read response
            response_line = self.process.stdout.readline()
            if not response_line:
                logger.error("No response from MCP server")
                return None
            
            response = json.loads(response_line.strip())
            
            if "error" in response:
                logger.error(f"MCP server error: {response['error']}")
                return None
            
            return response.get("result")
            
        except Exception as e:
            logger.error(f"Error sending MCP request: {e}")
            return None
    
    async def search_documents(self, query: str, k: int = 5, 
                             score_threshold: float = 0.3, 
                             search_type: str = "semantic") -> Optional[Dict[str, Any]]:
        """Search for documents using the RAG system."""
        try:
            params = {
                "query": query,
                "k": k,
                "score_threshold": score_threshold,
                "search_type": search_type
            }
            
            result = await self._send_request("tools/call", {
                "name": "search_documents",
                "arguments": params
            })
            
            if result and isinstance(result, list) and len(result) > 0:
                # Parse the JSON response from the tool
                tool_result = result[0].get("text", "{}")
                return json.loads(tool_result)
            
            return None
            
        except Exception as e:
            logger.error(f"Error searching documents: {e}")
            return None
    
    async def get_document(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific document by ID."""
        try:
            params = {"doc_id": doc_id}
            
            result = await self._send_request("tools/call", {
                "name": "get_document",
                "arguments": params
            })
            
            if result and isinstance(result, list) and len(result) > 0:
                tool_result = result[0].get("text", "{}")
                return json.loads(tool_result)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting document {doc_id}: {e}")
            return None
    
    async def get_similar_documents(self, doc_id: str, k: int = 3) -> Optional[Dict[str, Any]]:
        """Get similar documents to a given document."""
        try:
            params = {
                "doc_id": doc_id,
                "k": k
            }
            
            result = await self._send_request("tools/call", {
                "name": "get_similar_documents",
                "arguments": params
            })
            
            if result and isinstance(result, list) and len(result) > 0:
                tool_result = result[0].get("text", "{}")
                return json.loads(tool_result)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting similar documents for {doc_id}: {e}")
            return None
    
    async def list_collections(self) -> Optional[Dict[str, Any]]:
        """List collections and statistics."""
        try:
            result = await self._send_request("tools/call", {
                "name": "list_collections",
                "arguments": {}
            })
            
            if result and isinstance(result, list) and len(result) > 0:
                tool_result = result[0].get("text", "{}")
                return json.loads(tool_result)
            
            return None
            
        except Exception as e:
            logger.error(f"Error listing collections: {e}")
            return None
    
    async def add_document(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Add a document to the RAG system."""
        try:
            params = {"file_path": file_path}
            
            result = await self._send_request("tools/call", {
                "name": "add_document",
                "arguments": params
            })
            
            if result and isinstance(result, list) and len(result) > 0:
                tool_result = result[0].get("text", "{}")
                return json.loads(tool_result)
            
            return None
            
        except Exception as e:
            logger.error(f"Error adding document {file_path}: {e}")
            return None
    
    async def delete_document(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """Delete a document from the RAG system."""
        try:
            params = {"doc_id": doc_id}
            
            result = await self._send_request("tools/call", {
                "name": "delete_document",
                "arguments": params
            })
            
            if result and isinstance(result, list) and len(result) > 0:
                tool_result = result[0].get("text", "{}")
                return json.loads(tool_result)
            
            return None
            
        except Exception as e:
            logger.error(f"Error deleting document {doc_id}: {e}")
            return None
    
    def is_connected(self) -> bool:
        """Check if the client is connected."""
        return self._connected and self.process and self.process.poll() is None
    
    async def cleanup(self):
        """Clean up the MCP connection."""
        self._connected = False
        
        if self.process:
            try:
                # Try to terminate gracefully
                self.process.terminate()
                
                # Wait for termination
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # Force kill if it doesn't terminate
                    self.process.kill()
                    self.process.wait()
                
                logger.info("MCP server process terminated")
                
            except Exception as e:
                logger.warning(f"Error terminating MCP process: {e}")
            
            finally:
                self.process = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()
