#!/bin/bash
# RAG System Stop Script for Linux/macOS
# This script stops all RAG system services

echo ""
echo "========================================"
echo "   Stopping RAG System Services"
echo "========================================"
echo ""

# Function to kill process by PID file
kill_by_pidfile() {
    local pidfile=$1
    local service_name=$2
    
    if [ -f "$pidfile" ]; then
        local pid=$(cat "$pidfile")
        if kill -0 "$pid" 2>/dev/null; then
            echo "Stopping $service_name (PID: $pid)..."
            kill "$pid"
            sleep 2
            # Force kill if still running
            if kill -0 "$pid" 2>/dev/null; then
                echo "Force stopping $service_name..."
                kill -9 "$pid"
            fi
        else
            echo "$service_name was not running"
        fi
        rm -f "$pidfile"
    else
        echo "No PID file found for $service_name"
    fi
}

echo "[1/3] Stopping RAG Web Interface..."
kill_by_pidfile "logs/web_interface.pid" "Web Interface"

echo "[2/3] Stopping MCP Server..."
kill_by_pidfile "logs/mcp_server.pid" "MCP Server"

echo "[3/3] Cleaning up remaining processes..."

# Kill any remaining processes on port 8001
if command -v lsof >/dev/null 2>&1; then
    lsof -ti:8001 | xargs -r kill -9 2>/dev/null
fi

# Kill any remaining Python processes with RAG-related command lines
pkill -f "src.api.main" 2>/dev/null
pkill -f "src.mcp.server" 2>/dev/null
pkill -f "uvicorn.*src.api.main" 2>/dev/null

echo ""
echo "========================================"
echo "   RAG System Stopped Successfully!"
echo "========================================"
echo ""
echo "All RAG services have been terminated."
echo "You can restart the system by running ./start_rag.sh"
echo ""
echo "Log files are preserved in logs/ directory:"
if [ -d "logs" ]; then
    ls -la logs/
fi
echo ""
