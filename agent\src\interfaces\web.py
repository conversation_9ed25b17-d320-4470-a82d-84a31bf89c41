"""
Web interface for the Personal Assistant Agent.
"""
import asyncio
import json
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from loguru import logger

from ..core.agent import PersonalAssistantAgent
from ...config.agent_settings import settings


# Pydantic models
class ChatMessage(BaseModel):
    message: str
    conversation_id: str = None


class ChatResponse(BaseModel):
    response: str
    conversation_id: str
    timestamp: str


class SystemStatus(BaseModel):
    status: Dict[str, Any]


# FastAPI app
app = FastAPI(
    title="Personal Assistant Agent",
    description="Web interface for the Personal Assistant Agent",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins + ["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global agent instance
agent: PersonalAssistantAgent = None


@app.on_event("startup")
async def startup_event():
    """Initialize the agent on startup."""
    global agent
    logger.info("Starting Personal Assistant Agent web interface...")
    
    agent = PersonalAssistantAgent()
    if not await agent.start():
        logger.error("Failed to start agent")
        raise RuntimeError("Failed to start agent")
    
    logger.info("Agent web interface started successfully")


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    global agent
    if agent:
        await agent.stop()
        logger.info("Agent stopped")


@app.get("/", response_class=HTMLResponse)
async def get_index():
    """Serve the main chat interface."""
    html_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Personal Assistant Agent</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .message.user .message-content {
            background: #007bff;
            color: white;
        }
        
        .message.assistant .message-content {
            background: white;
            border: 1px solid #e9ecef;
            color: #333;
        }
        
        .message-time {
            font-size: 11px;
            opacity: 0.6;
            margin-top: 5px;
        }
        
        .chat-input {
            padding: 20px;
            border-top: 1px solid #e9ecef;
            background: white;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
        }
        
        .input-group input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
        }
        
        .input-group input:focus {
            border-color: #007bff;
        }
        
        .input-group button {
            padding: 12px 24px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }
        
        .input-group button:hover {
            background: #0056b3;
        }
        
        .input-group button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .status {
            padding: 10px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            font-size: 12px;
            color: #666;
        }
        
        .typing-indicator {
            display: none;
            padding: 10px 16px;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 18px;
            margin-bottom: 15px;
            max-width: 70%;
        }
        
        .typing-dots {
            display: flex;
            gap: 4px;
        }
        
        .typing-dots span {
            width: 8px;
            height: 8px;
            background: #ccc;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }
        
        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }
        
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>Personal Assistant</h1>
            <p>Your AI assistant with access to your knowledge base</p>
        </div>
        
        <div class="chat-messages" id="messages">
            <div class="message assistant">
                <div class="message-content">
                    Hello! I'm your personal assistant. I have access to your knowledge base and can help answer questions, provide information, and assist with various tasks. How can I help you today?
                    <div class="message-time" id="welcome-time"></div>
                </div>
            </div>
        </div>
        
        <div class="typing-indicator" id="typing">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
        
        <div class="chat-input">
            <div class="input-group">
                <input type="text" id="messageInput" placeholder="Type your message..." maxlength="1000">
                <button id="sendButton">Send</button>
            </div>
        </div>
        
        <div class="status" id="status">
            Ready
        </div>
    </div>

    <script>
        const messagesContainer = document.getElementById('messages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const status = document.getElementById('status');
        const typing = document.getElementById('typing');
        
        // Set welcome message time
        document.getElementById('welcome-time').textContent = new Date().toLocaleTimeString();
        
        // WebSocket connection
        const ws = new WebSocket(`ws://localhost:${window.location.port}/ws`);
        
        ws.onopen = function(event) {
            status.textContent = 'Connected';
            status.style.color = '#28a745';
        };
        
        ws.onclose = function(event) {
            status.textContent = 'Disconnected';
            status.style.color = '#dc3545';
        };
        
        ws.onerror = function(error) {
            status.textContent = 'Connection error';
            status.style.color = '#dc3545';
        };
        
        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            
            if (data.type === 'response_chunk') {
                appendToLastMessage(data.content);
            } else if (data.type === 'response_complete') {
                hideTyping();
                enableInput();
            } else if (data.type === 'error') {
                hideTyping();
                addMessage('assistant', 'Sorry, I encountered an error: ' + data.message);
                enableInput();
            }
        };
        
        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;
            
            addMessage('user', message);
            messageInput.value = '';
            disableInput();
            showTyping();
            
            ws.send(JSON.stringify({
                type: 'chat_message',
                message: message
            }));
        }
        
        function addMessage(role, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.innerHTML = content.replace(/\\n/g, '<br>');
            
            const timeDiv = document.createElement('div');
            timeDiv.className = 'message-time';
            timeDiv.textContent = new Date().toLocaleTimeString();
            
            contentDiv.appendChild(timeDiv);
            messageDiv.appendChild(contentDiv);
            messagesContainer.appendChild(messageDiv);
            
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function appendToLastMessage(content) {
            const lastMessage = messagesContainer.lastElementChild;
            if (lastMessage && lastMessage.classList.contains('assistant')) {
                const contentDiv = lastMessage.querySelector('.message-content');
                const timeDiv = contentDiv.querySelector('.message-time');
                const currentContent = contentDiv.innerHTML.replace(timeDiv.outerHTML, '');
                contentDiv.innerHTML = currentContent + content + timeDiv.outerHTML;
            } else {
                // Create new assistant message
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message assistant';
                
                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.innerHTML = content;
                
                const timeDiv = document.createElement('div');
                timeDiv.className = 'message-time';
                timeDiv.textContent = new Date().toLocaleTimeString();
                
                contentDiv.appendChild(timeDiv);
                messageDiv.appendChild(contentDiv);
                messagesContainer.appendChild(messageDiv);
            }
            
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function showTyping() {
            typing.style.display = 'block';
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function hideTyping() {
            typing.style.display = 'none';
        }
        
        function disableInput() {
            messageInput.disabled = true;
            sendButton.disabled = true;
            status.textContent = 'Thinking...';
            status.style.color = '#ffc107';
        }
        
        function enableInput() {
            messageInput.disabled = false;
            sendButton.disabled = false;
            messageInput.focus();
            status.textContent = 'Ready';
            status.style.color = '#28a745';
        }
        
        // Event listeners
        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // Focus input on load
        messageInput.focus();
    </script>
</body>
</html>
    """
    return HTMLResponse(content=html_content)


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time chat."""
    await websocket.accept()
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            if message_data.get("type") == "chat_message":
                user_message = message_data.get("message", "")
                
                if user_message.strip():
                    try:
                        # Process message and stream response
                        async for chunk in agent.process_message(user_message):
                            await websocket.send_text(json.dumps({
                                "type": "response_chunk",
                                "content": chunk
                            }))
                        
                        # Send completion signal
                        await websocket.send_text(json.dumps({
                            "type": "response_complete"
                        }))
                        
                    except Exception as e:
                        logger.error(f"Error processing message: {e}")
                        await websocket.send_text(json.dumps({
                            "type": "error",
                            "message": str(e)
                        }))
                        
    except WebSocketDisconnect:
        logger.info("WebSocket client disconnected")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")


@app.get("/api/status", response_model=SystemStatus)
async def get_status():
    """Get system status."""
    if not agent:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    return SystemStatus(status=agent.get_system_status())


@app.get("/api/conversations")
async def get_conversations():
    """Get conversation list."""
    if not agent:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    return {"conversations": agent.get_conversation_list()}


@app.get("/api/memory")
async def get_memory_stats():
    """Get memory statistics."""
    if not agent:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    return {"memory": agent.get_memory_stats()}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "web:app",
        host="localhost",
        port=settings.web_port,
        reload=True
    )
