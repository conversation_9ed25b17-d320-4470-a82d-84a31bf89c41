#!/usr/bin/env python3
"""
Command-line interface for the Personal Assistant Agent.
"""
import asyncio
import sys
from pathlib import Path

# Add the agent directory to the path
agent_root = Path(__file__).parent
sys.path.insert(0, str(agent_root))

import typer
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt
from rich.live import Live
from rich.text import Text
from rich.table import Table
from loguru import logger

from src.core.agent import PersonalAssistantAgent
from config.agent_settings import settings

app = typer.Typer(help="Personal Assistant Agent CLI")
console = Console()


@app.command()
def chat():
    """Start an interactive chat session with the agent."""
    console.print(Panel.fit(
        "[bold blue]Personal Assistant Agent[/bold blue]\n"
        "Type 'quit', 'exit', or 'bye' to end the session.\n"
        "Type 'help' for available commands.",
        style="bold blue"
    ))
    
    asyncio.run(_chat_session())


async def _chat_session():
    """Run the interactive chat session."""
    agent = PersonalAssistantAgent()
    
    try:
        # Initialize the agent
        console.print("[yellow]Initializing agent...[/yellow]")
        if not await agent.start():
            console.print("[red]Failed to start agent[/red]")
            return
        
        console.print("[green]Agent ready! Start chatting...[/green]\n")
        
        while True:
            try:
                # Get user input
                user_input = Prompt.ask("[bold cyan]You[/bold cyan]")
                
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    break
                
                if user_input.lower() == 'help':
                    _show_help()
                    continue
                
                if user_input.lower() == 'status':
                    _show_status(agent)
                    continue
                
                if user_input.lower() == 'conversations':
                    _show_conversations(agent)
                    continue
                
                if user_input.lower() == 'memory':
                    _show_memory(agent)
                    continue
                
                if not user_input.strip():
                    continue
                
                # Process the message and stream the response
                console.print("[bold green]Assistant:[/bold green] ", end="")
                
                response_text = ""
                async for chunk in agent.process_message(user_input):
                    console.print(chunk, end="")
                    response_text += chunk
                
                console.print("\n")  # New line after response
                
            except KeyboardInterrupt:
                console.print("\n[yellow]Use 'quit' to exit gracefully[/yellow]")
                continue
            except Exception as e:
                console.print(f"\n[red]Error: {e}[/red]")
                continue
    
    finally:
        console.print("[yellow]Shutting down agent...[/yellow]")
        await agent.stop()
        console.print("[green]Goodbye![/green]")


def _show_help():
    """Show help information."""
    help_table = Table(title="Available Commands")
    help_table.add_column("Command", style="cyan")
    help_table.add_column("Description", style="white")
    
    help_table.add_row("help", "Show this help message")
    help_table.add_row("status", "Show agent status")
    help_table.add_row("conversations", "List recent conversations")
    help_table.add_row("memory", "Show memory statistics")
    help_table.add_row("quit/exit/bye", "Exit the chat session")
    
    console.print(help_table)


def _show_status(agent: PersonalAssistantAgent):
    """Show agent status."""
    status = agent.get_system_status()
    
    status_table = Table(title="Agent Status")
    status_table.add_column("Component", style="cyan")
    status_table.add_column("Status", style="white")
    
    status_table.add_row("Initialized", "✓" if status['initialized'] else "✗")
    status_table.add_row("Running", "✓" if status['running'] else "✗")
    status_table.add_row("MCP Connected", "✓" if status['mcp_connected'] else "✗")
    status_table.add_row("Ollama Connected", "✓" if status['ollama_connected'] else "✗")
    status_table.add_row("Conversations", str(status['conversations']))
    
    console.print(status_table)


def _show_conversations(agent: PersonalAssistantAgent):
    """Show recent conversations."""
    conversations = agent.get_conversation_list()
    
    if not conversations:
        console.print("[yellow]No conversations found[/yellow]")
        return
    
    conv_table = Table(title="Recent Conversations")
    conv_table.add_column("ID", style="cyan")
    conv_table.add_column("Title", style="white")
    conv_table.add_column("Messages", style="green")
    conv_table.add_column("Updated", style="yellow")
    
    for conv in conversations[:10]:  # Show last 10
        conv_table.add_row(
            conv['id'][:8] + "...",
            conv['title'][:30] + "..." if len(conv['title']) > 30 else conv['title'],
            str(conv['message_count']),
            conv['updated_at'][:16]  # Date and time only
        )
    
    console.print(conv_table)


def _show_memory(agent: PersonalAssistantAgent):
    """Show memory statistics."""
    stats = agent.get_memory_stats()
    
    if not stats:
        console.print("[yellow]No memory statistics available[/yellow]")
        return
    
    memory_table = Table(title="Memory Statistics")
    memory_table.add_column("Metric", style="cyan")
    memory_table.add_column("Value", style="white")
    
    memory_table.add_row("Total Memories", str(stats.get('total_memories', 0)))
    memory_table.add_row("Cache Size", str(stats.get('cache_size', 0)))
    
    # Show memory types
    by_type = stats.get('by_type', {})
    for mem_type, count in by_type.items():
        memory_table.add_row(f"{mem_type.title()} Memories", str(count))
    
    # Show importance stats
    importance = stats.get('importance', {})
    if importance:
        memory_table.add_row("Avg Importance", f"{importance.get('average', 0):.2f}")
        memory_table.add_row("Max Importance", f"{importance.get('maximum', 0):.2f}")
    
    console.print(memory_table)


@app.command()
def test():
    """Test agent connections and functionality."""
    console.print("[yellow]Testing agent functionality...[/yellow]")
    asyncio.run(_test_agent())


async def _test_agent():
    """Test the agent."""
    agent = PersonalAssistantAgent()
    
    try:
        # Test initialization
        console.print("1. Testing initialization...")
        if await agent.start():
            console.print("   ✓ Agent initialized successfully")
        else:
            console.print("   ✗ Agent initialization failed")
            return
        
        # Test status
        console.print("2. Testing status...")
        status = agent.get_system_status()
        console.print(f"   ✓ Status retrieved: {status}")
        
        # Test simple query
        console.print("3. Testing simple query...")
        test_query = "Hello, can you help me?"
        
        response_text = ""
        async for chunk in agent.process_message(test_query):
            response_text += chunk
        
        if response_text:
            console.print(f"   ✓ Response generated: {response_text[:100]}...")
        else:
            console.print("   ✗ No response generated")
        
        console.print("[green]All tests completed![/green]")
        
    except Exception as e:
        console.print(f"[red]Test failed: {e}[/red]")
    
    finally:
        await agent.stop()


@app.command()
def config():
    """Show current configuration."""
    config_table = Table(title="Agent Configuration")
    config_table.add_column("Setting", style="cyan")
    config_table.add_column("Value", style="white")
    
    config_table.add_row("Agent Name", settings.agent_name)
    config_table.add_row("Personality", settings.agent_personality)
    config_table.add_row("Ollama URL", settings.ollama_base_url)
    config_table.add_row("Ollama Model", settings.ollama_model)
    config_table.add_row("RAG Threshold", str(settings.rag_search_threshold))
    config_table.add_row("Max RAG Results", str(settings.max_rag_results))
    config_table.add_row("Max Context Length", str(settings.max_context_length))
    config_table.add_row("Web Port", str(settings.web_port))
    config_table.add_row("API Port", str(settings.api_port))
    
    console.print(config_table)


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sys.stderr,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    app()
