@echo off
REM Start script for Personal Assistant Agent (Windows)
echo ========================================
echo   Personal Assistant Agent Startup
echo ========================================
echo.

REM Check if we're in the right directory
if not exist "agent_manager.py" (
    echo Error: agent_manager.py not found!
    echo Please run this script from the agent directory.
    echo.
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv_agent" (
    echo Virtual environment not found. Running setup...
    echo.
    python setup_agent.py
    if errorlevel 1 (
        echo Setup failed! Please check the errors above.
        pause
        exit /b 1
    )
    echo.
)

REM Activate virtual environment
echo [1/4] Activating virtual environment...
call venv_agent\Scripts\activate.bat
if errorlevel 1 (
    echo Failed to activate virtual environment!
    pause
    exit /b 1
)

REM Check if RAG system is running
echo [2/4] Checking RAG system...
cd ..
python -c "import requests; requests.get('http://localhost:8001/api/status', timeout=2)" >nul 2>&1
if errorlevel 1 (
    echo RAG system not detected. Starting RAG system first...
    call start_rag.bat
    timeout /t 5 /nobreak >nul
    echo RAG system started. Continuing with agent...
) else (
    echo RAG system is already running.
)
cd agent

REM Check Ollama
echo [3/4] Checking Ollama...
ollama list >nul 2>&1
if errorlevel 1 (
    echo Warning: Ollama not found or not running.
    echo Please ensure Ollama is installed and running.
    echo You can download it from: https://ollama.ai/
    echo.
    echo Continuing anyway - some features may not work...
) else (
    echo Ollama is available.
)

REM Start the agent
echo [4/4] Starting Personal Assistant Agent...
echo.
echo Starting agent services...
echo Web interface will be available at: http://localhost:8002
echo.

REM Start using the agent manager
python agent_manager.py start

if errorlevel 1 (
    echo.
    echo Failed to start the agent! Check the error messages above.
    pause
    exit /b 1
)

echo.
echo ========================================
echo   Personal Assistant Agent Started!
echo ========================================
echo.
echo Web Interface: http://localhost:8002
echo CLI Chat: python agent_cli.py chat
echo Status: python agent_manager.py status
echo Stop: python agent_manager.py stop
echo.
echo The web interface should open automatically.
echo Press any key to continue...
pause >nul
