[32m2025-07-08 17:49:02.998[0m | [33m[1mWARNING [0m | [36msrc.core.embeddings[0m:[36m_initialize_device[0m:[36m33[0m - [33m[1mCUDA requested but not available, falling back to CPU[0m
[32m2025-07-08 17:49:02.998[0m | [1mINFO    [0m | [36msrc.core.embeddings[0m:[36m_initialize_device[0m:[36m36[0m - [1mEmbedding device set to: cpu[0m
[32m2025-07-08 17:49:02.999[0m | [1mINFO    [0m | [36msrc.processing.pipeline[0m:[36m_initialize_extractors[0m:[36m42[0m - [1mInitialized extractors for: ['.txt', '.md', '.rst', '.log', '.csv', '.json', '.xml', '.yaml', '.yml', '.pdf', '.docx', '.html', '.htm'][0m
[32m2025-07-08 17:49:02.999[0m | [1mINFO    [0m | [36msrc.core.embeddings[0m:[36mload_model[0m:[36m41[0m - [1mLoading embedding model: sentence-transformers/all-MiniLM-L6-v2[0m
[32m2025-07-08 17:49:05.878[0m | [1mINFO    [0m | [36msrc.core.embeddings[0m:[36mload_model[0m:[36m43[0m - [1mEmbedding model loaded successfully[0m
[32m2025-07-08 17:49:05.878[0m | [1mINFO    [0m | [36msrc.core.vectorstore[0m:[36m_initialize_index[0m:[36m70[0m - [1mInitialized FAISS index with dimension 384[0m
[32m2025-07-08 17:49:05.879[0m | [1mINFO    [0m | [36msrc.core.vectorstore[0m:[36mload[0m:[36m231[0m - [1mLoaded vector store with 27 documents[0m
[32m2025-07-08 17:49:05.879[0m | [1mINFO    [0m | [36msrc.mcp.tools[0m:[36m_initialize_vector_store[0m:[36m46[0m - [1mRAG tools initialized successfully[0m
[32m2025-07-08 17:49:05.879[0m | [1mINFO    [0m | [36m__main__[0m:[36mrun[0m:[36m210[0m - [1mStarting RAG MCP server...[0m
