#!/bin/bash
# Stop script for Personal Assistant Agent (Linux/macOS)

echo "========================================"
echo "   Personal Assistant Agent Shutdown"
echo "========================================"
echo

# Check if we're in the right directory
if [ ! -f "agent_manager.py" ]; then
    echo "Error: agent_manager.py not found!"
    echo "Please run this script from the agent directory."
    echo
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "venv_agent" ]; then
    echo "Virtual environment not found."
    echo "The agent may not be running."
    echo
    cleanup_manual
    exit 0
fi

# Function for manual cleanup
cleanup_manual() {
    echo
    echo "Performing manual cleanup..."
    
    # Kill processes on agent ports
    echo "Checking for remaining agent processes..."
    
    # Find and kill processes on port 8002
    PID_8002=$(lsof -ti:8002 2>/dev/null)
    if [ ! -z "$PID_8002" ]; then
        echo "Terminating process on port 8002 (PID: $PID_8002)"
        kill -TERM $PID_8002 2>/dev/null
        sleep 2
        kill -KILL $PID_8002 2>/dev/null
    fi
    
    # Find and kill processes on port 8003
    PID_8003=$(lsof -ti:8003 2>/dev/null)
    if [ ! -z "$PID_8003" ]; then
        echo "Terminating process on port 8003 (PID: $PID_8003)"
        kill -TERM $PID_8003 2>/dev/null
        sleep 2
        kill -KILL $PID_8003 2>/dev/null
    fi
    
    # Clean up PID files if they exist
    if [ -f "data/logs/web_interface.pid" ]; then
        echo "Cleaning up PID files..."
        rm -f "data/logs/web_interface.pid" 2>/dev/null
    fi
    
    if [ -f "data/logs/api_server.pid" ]; then
        rm -f "data/logs/api_server.pid" 2>/dev/null
    fi
    
    echo "Manual cleanup completed."
}

# Activate virtual environment
echo "Activating virtual environment..."
source venv_agent/bin/activate
if [ $? -ne 0 ]; then
    echo "Failed to activate virtual environment!"
    echo "Attempting manual cleanup..."
    cleanup_manual
    exit 1
fi

# Stop the agent using the manager
echo "Stopping Personal Assistant Agent..."
python3 agent_manager.py stop

if [ $? -ne 0 ]; then
    echo "Warning: Agent manager reported issues during shutdown."
    echo "Attempting manual cleanup..."
    cleanup_manual
else
    echo
    echo "Agent stopped successfully!"
fi

echo
echo "========================================"
echo "   Personal Assistant Agent Stopped"
echo "========================================"
echo
echo "All agent services have been terminated."
echo "You can restart the agent with: ./start_agent.sh"
echo
